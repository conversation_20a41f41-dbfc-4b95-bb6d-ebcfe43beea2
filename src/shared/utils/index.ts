/**
 * 统一工具函数
 * 🎯 核心价值：项目中所有通用工具函数的统一定义
 * 📦 功能范围：数据处理、验证、格式化、性能优化等
 * 🔄 架构设计：函数式编程，纯函数设计，易于测试
 */

import type { Coordinate, BasicColorType, DataLevel, WordLibraryKey } from '../types';

// ===== 坐标工具函数 =====

/** 生成坐标键 */
export const coordinateKey = (coord: Coordinate): string => `${coord.x},${coord.y}`;

/** 解析坐标键 */
export const parseCoordinateKey = (key: string): Coordinate => {
  const [x, y] = key.split(',').map(Number);
  return { x, y };
};

/** 检查坐标是否有效 */
export const isValidCoordinate = (coord: Coordinate, size: number = 33): boolean => {
  return coord.x >= 0 && coord.x < size && coord.y >= 0 && coord.y < size;
};

/** 计算坐标距离 */
export const calculateDistance = (coord1: Coordinate, coord2: Coordinate): number => {
  return Math.sqrt(Math.pow(coord2.x - coord1.x, 2) + Math.pow(coord2.y - coord1.y, 2));
};

/** 获取相邻坐标 */
export const getAdjacentCoordinates = (coord: Coordinate, size: number = 33): Coordinate[] => {
  const directions = [
    { x: -1, y: 0 }, { x: 1, y: 0 },  // 左右
    { x: 0, y: -1 }, { x: 0, y: 1 },  // 上下
  ];
  
  return directions
    .map(dir => ({ x: coord.x + dir.x, y: coord.y + dir.y }))
    .filter(newCoord => isValidCoordinate(newCoord, size));
};

// ===== 颜色工具函数 =====

/** 颜色到CSS类名映射 */
const COLOR_CLASS_MAP: Record<BasicColorType, string> = {
  red: 'bg-red-500',
  blue: 'bg-blue-500',
  green: 'bg-green-500',
  yellow: 'bg-yellow-500',
  purple: 'bg-purple-500',
  orange: 'bg-orange-500',
  pink: 'bg-pink-500',
  cyan: 'bg-cyan-500',
  black: 'bg-black',
  white: 'bg-white',
  gray: 'bg-gray-500',
  brown: 'bg-amber-700',
};

/** 获取颜色CSS类名 */
export const getColorClassName = (color: BasicColorType): string => {
  return COLOR_CLASS_MAP[color] || 'bg-white';
};

/** 颜色到十六进制映射 */
const COLOR_HEX_MAP: Record<BasicColorType, string> = {
  red: '#ef4444',
  blue: '#3b82f6',
  green: '#22c55e',
  yellow: '#eab308',
  purple: '#a855f7',
  orange: '#f97316',
  pink: '#ec4899',
  cyan: '#06b6d4',
  black: '#000000',
  white: '#ffffff',
  gray: '#6b7280',
  brown: '#b45309',
};

/** 获取颜色十六进制值 */
export const getColorHex = (color: BasicColorType): string => {
  return COLOR_HEX_MAP[color] || '#ffffff';
};

// ===== 词库工具函数 =====

/** 生成词库键 */
export const createWordLibraryKey = (color: BasicColorType, level: DataLevel): WordLibraryKey => {
  return `${color}-${level}`;
};

/** 解析词库键 */
export const parseWordLibraryKey = (key: WordLibraryKey): { color: BasicColorType; level: DataLevel } => {
  const [color, levelStr] = key.split('-');
  return {
    color: color as BasicColorType,
    level: parseInt(levelStr) as DataLevel,
  };
};

/** 获取词库显示名称 */
export const getWordLibraryDisplayName = (key: WordLibraryKey): string => {
  const { color, level } = parseWordLibraryKey(key);
  const colorNames: Record<BasicColorType, string> = {
    red: '红色', blue: '蓝色', green: '绿色', yellow: '黄色',
    purple: '紫色', orange: '橙色', pink: '粉色', cyan: '青色',
    black: '黑色', white: '白色', gray: '灰色', brown: '棕色',
  };
  return `${colorNames[color]}${level}级`;
};

// ===== 验证工具函数 =====

/** 验证词语格式 */
export const validateWord = (word: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!word || word.trim().length === 0) {
    errors.push('词语不能为空');
  }
  
  if (word.length > 10) {
    errors.push('词语长度不能超过10个字符');
  }
  
  if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/.test(word)) {
    errors.push('词语只能包含中文、英文、数字和空格');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/** 验证坐标范围 */
export const validateCoordinateRange = (coord: Coordinate, size: number = 33): boolean => {
  return coord.x >= 0 && coord.x < size && coord.y >= 0 && coord.y < size;
};

// ===== 性能优化工具函数 =====

/** 防抖函数 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/** 节流函数 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  interval: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    const timeSinceLastCall = now - lastCall;
    
    if (timeSinceLastCall >= interval) {
      lastCall = now;
      func(...args);
    } else {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        lastCall = Date.now();
        func(...args);
      }, interval - timeSinceLastCall);
    }
  };
};

/** 深拷贝函数 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
};

// ===== 格式化工具函数 =====

/** 格式化时间 */
export const formatTime = (date: Date): string => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

/** 格式化文件大小 */
export const formatFileSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
};

// ===== 数组工具函数 =====

/** 数组去重 */
export const unique = <T>(array: T[]): T[] => {
  return Array.from(new Set(array));
};

/** 数组分组 */
export const groupBy = <T, K extends string | number>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> => {
  return array.reduce((groups, item) => {
    const key = keyFn(item);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {} as Record<K, T[]>);
};

/** 数组分块 */
export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

// ===== 对象工具函数 =====

/** 对象键值对交换 */
export const invert = <T extends Record<string, string>>(obj: T): Record<string, string> => {
  const inverted: Record<string, string> = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      inverted[obj[key]] = key;
    }
  }
  return inverted;
};

/** 对象深度合并 */
export const deepMerge = <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key];
      const targetValue = result[key];
      
      if (
        sourceValue &&
        typeof sourceValue === 'object' &&
        !Array.isArray(sourceValue) &&
        targetValue &&
        typeof targetValue === 'object' &&
        !Array.isArray(targetValue)
      ) {
        result[key] = deepMerge(targetValue, sourceValue);
      } else {
        result[key] = sourceValue as T[Extract<keyof T, string>];
      }
    }
  }
  
  return result;
};
