/**
 * 统一类型定义 - Next.js App Router 版本
 * 🎯 核心价值：项目中所有类型的统一定义，避免重复和不一致
 * 📦 功能范围：矩阵、词库、UI、配置等所有类型
 * 🔄 架构设计：分层类型定义，支持类型复用和扩展
 * 📁 位置：apps/frontend/lib/types/index.ts
 */

// ===== 基础类型 =====

/** 坐标类型 */
export interface Coordinate {
  x: number;
  y: number;
}

/** 基础颜色类型 */
export type BasicColorType =
  | 'red' | 'blue' | 'green' | 'yellow'
  | 'purple' | 'orange' | 'pink' | 'cyan'
  | 'black' | 'white' | 'gray' | 'brown';

/** 数据级别 */
export type DataLevel = 1 | 2 | 3 | 4;

/** 词库键类型 */
export type WordLibraryKey = `${BasicColorType}-${DataLevel}`;

// ===== 矩阵相关类型 =====

/** 单元格数据 */
export interface CellData {
  /** 坐标 */
  coordinate: Coordinate;
  /** 颜色 */
  color: BasicColorType;
  /** 数据级别 */
  level: DataLevel;
  /** 数值 */
  value?: number;
  /** 词语 */
  word?: string;
  /** 是否激活 */
  isActive: boolean;
  /** 分组 */
  group?: string;
}

/** 单元格渲染数据 */
export interface CellRenderData {
  /** 显示内容 */
  content: string;
  /** CSS类名 */
  className: string;
  /** 内联样式 */
  style: React.CSSProperties;
  /** 是否可交互 */
  isInteractive: boolean;
}

/** 矩阵数据 */
export interface MatrixData {
  /** 所有单元格数据 */
  cells: CellData[];
  /** 矩阵尺寸 */
  size: number;
  /** 版本 */
  version: string;
  /** 最后更新时间 */
  lastUpdated: Date;
}

/** 显示模式 */
export type MainMode = 'color' | 'number';
export type ContentMode = 'word' | 'number' | 'coordinate';
export type BusinessMode = 'coordinate' | 'color' | 'level' | 'word' | 'color-word' | 'number-word';

/** 矩阵配置 */
export interface MatrixConfig {
  /** 业务模式 */
  mode: BusinessMode;
  /** 主模式 */
  mainMode?: MainMode;
  /** 内容模式 */
  contentMode?: ContentMode;
  /** 是否颜色模式 */
  isColorMode?: boolean;
  /** 是否词语模式 */
  isWordMode?: boolean;
  /** 是否数字模式 */
  isNumberMode?: boolean;
  /** 是否坐标模式 */
  isCoordinateMode?: boolean;
}

// ===== 词库相关类型 =====

/** 词库数据 */
export interface WordLibrary {
  /** 词库键 */
  key: WordLibraryKey;
  /** 词语列表 */
  words: string[];
  /** 显示名称 */
  displayName: string;
  /** 背景颜色 */
  backgroundColor: string;
  /** 文字颜色 */
  textColor: string;
}

/** 词语验证结果 */
export interface WordValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
  /** 建议 */
  suggestions: string[];
}

// ===== 事件相关类型 =====

/** 交互事件 */
export interface InteractionEvent {
  /** 事件类型 */
  type: 'click' | 'hover' | 'focus' | 'keyboard';
  /** 坐标 */
  coordinate: Coordinate;
  /** 原始事件 */
  originalEvent: Event;
  /** 时间戳 */
  timestamp: number;
}

/** 事件处理器选项 */
export interface EventHandlerOptions {
  /** 是否启用防抖 */
  enableDebounce?: boolean;
  /** 防抖延迟 */
  debounceDelay?: number;
  /** 是否启用节流 */
  enableThrottle?: boolean;
  /** 节流延迟 */
  throttleDelay?: number;
  /** 是否阻止默认行为 */
  preventDefault?: boolean;
  /** 是否阻止事件冒泡 */
  stopPropagation?: boolean;
}

// ===== 样式相关类型 =====

/** 样式主题 */
export interface StyleTheme {
  primaryColor?: string;
  secondaryColor?: string;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
}

/** 样式计算选项 */
export interface StyleCalculationOptions {
  /** 单元格坐标 */
  coordinate: Coordinate;
  /** 单元格数据 */
  cellData?: CellData;
  /** 渲染数据 */
  renderData?: CellRenderData;
  /** 矩阵配置 */
  config?: MatrixConfig;
  /** 是否为增强模式 */
  isEnhanced?: boolean;
  /** 是否为填词模式活跃状态 */
  isWordInputActive?: boolean;
  /** 自定义样式 */
  customStyle?: React.CSSProperties;
  /** 自定义类名 */
  customClassName?: string;
  /** 条件类名映射 */
  conditionalClasses?: Record<string, boolean>;
  /** 是否使用BEM规范 */
  useBEM?: boolean;
  /** 主题配置 */
  theme?: StyleTheme;
}

// ===== 状态相关类型 =====

/** 应用状态 */
export interface AppState {
  /** 矩阵数据 */
  matrixData: MatrixData;
  /** 矩阵配置 */
  matrixConfig: MatrixConfig;
  /** 词库数据 */
  wordLibraries: Record<WordLibraryKey, WordLibrary>;
  /** 当前选中的坐标 */
  selectedCoordinate?: Coordinate;
  /** 是否处于词语输入模式 */
  isWordInputMode: boolean;
  /** UI状态 */
  ui: {
    /** 是否显示加载状态 */
    isLoading: boolean;
    /** 错误信息 */
    error?: string;
    /** 成功信息 */
    success?: string;
  };
}

// ===== 工具类型 =====

/** 坐标键生成函数 */
export const coordinateKey = (coord: Coordinate): string => `${coord.x},${coord.y}`;

/** 创建默认单元格 */
export const createDefaultCell = (coordinate: Coordinate): CellData => ({
  coordinate,
  color: 'white',
  level: 1,
  isActive: false,
});

/** 矩阵常量 */
export const MATRIX_SIZE = 33;
export const TOTAL_CELLS = MATRIX_SIZE * MATRIX_SIZE;

/** 默认矩阵配置 */
export const DEFAULT_MATRIX_CONFIG: MatrixConfig = {
  mode: 'color-word',
  mainMode: 'color',
  contentMode: 'word',
  isColorMode: true,
  isWordMode: true,
  isNumberMode: false,
  isCoordinateMode: false,
};
