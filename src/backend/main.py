"""
后端主应用 - 整合版本
🎯 核心价值：统一的FastAPI应用，整合所有后端功能
📦 功能范围：API路由、数据模型、业务服务、中间件
🔄 架构设计：单文件应用，简化部署和维护
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn
import logging

# ===== 配置和常量 =====

# 应用配置
APP_CONFIG = {
    "title": "Cube1 Group Backend API",
    "description": "统一的后端API服务，支持矩阵数据和词库管理",
    "version": "1.0.0",
    "docs_url": "/docs",
    "redoc_url": "/redoc",
}

# CORS配置
CORS_CONFIG = {
    "allow_origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
    "allow_credentials": True,
    "allow_methods": ["*"],
    "allow_headers": ["*"],
}

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# ===== 数据模型 =====

class Coordinate(BaseModel):
    """坐标模型"""
    x: int = Field(..., ge=0, le=32, description="X坐标 (0-32)")
    y: int = Field(..., ge=0, le=32, description="Y坐标 (0-32)")

class CellData(BaseModel):
    """单元格数据模型"""
    coordinate: Coordinate
    color: str = Field(..., description="颜色")
    level: int = Field(..., ge=1, le=4, description="级别 (1-4)")
    value: Optional[int] = Field(None, description="数值")
    word: Optional[str] = Field(None, max_length=10, description="词语")
    is_active: bool = Field(False, description="是否激活")
    group: Optional[str] = Field(None, description="分组")

class MatrixData(BaseModel):
    """矩阵数据模型"""
    cells: List[CellData] = Field(..., description="单元格列表")
    size: int = Field(33, description="矩阵尺寸")
    version: str = Field("1.0.0", description="版本号")
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")

class MatrixConfig(BaseModel):
    """矩阵配置模型"""
    mode: str = Field("color-word", description="显示模式")
    main_mode: str = Field("color", description="主模式")
    content_mode: str = Field("word", description="内容模式")
    is_color_mode: bool = Field(True, description="是否颜色模式")
    is_word_mode: bool = Field(True, description="是否词语模式")

class WordLibrary(BaseModel):
    """词库模型"""
    key: str = Field(..., description="词库键")
    words: List[str] = Field(default_factory=list, description="词语列表")
    display_name: str = Field(..., description="显示名称")
    background_color: str = Field(..., description="背景颜色")
    text_color: str = Field(..., description="文字颜色")

class APIResponse(BaseModel):
    """统一API响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

# ===== 数据存储 =====

class DataStore:
    """简单的内存数据存储"""
    
    def __init__(self):
        self.matrix_data: Optional[MatrixData] = None
        self.matrix_config: MatrixConfig = MatrixConfig()
        self.word_libraries: Dict[str, WordLibrary] = {}
        self._initialize_default_data()
    
    def _initialize_default_data(self):
        """初始化默认数据"""
        # 初始化默认矩阵数据
        cells = []
        for x in range(33):
            for y in range(33):
                cells.append(CellData(
                    coordinate=Coordinate(x=x, y=y),
                    color="white",
                    level=1,
                    is_active=False
                ))
        
        self.matrix_data = MatrixData(cells=cells)
        
        # 初始化默认词库
        colors = ["red", "blue", "green", "yellow", "purple", "orange", "pink", "cyan"]
        levels = [1, 2, 3, 4]
        
        for color in colors:
            for level in levels:
                key = f"{color}-{level}"
                self.word_libraries[key] = WordLibrary(
                    key=key,
                    words=[],
                    display_name=f"{color.title()}{level}级",
                    background_color=self._get_color_hex(color),
                    text_color="#000000" if color != "black" else "#ffffff"
                )
    
    def _get_color_hex(self, color: str) -> str:
        """获取颜色十六进制值"""
        color_map = {
            "red": "#ef4444", "blue": "#3b82f6", "green": "#22c55e",
            "yellow": "#eab308", "purple": "#a855f7", "orange": "#f97316",
            "pink": "#ec4899", "cyan": "#06b6d4", "black": "#000000",
            "white": "#ffffff", "gray": "#6b7280", "brown": "#b45309"
        }
        return color_map.get(color, "#ffffff")

# 全局数据存储实例
data_store = DataStore()

# ===== FastAPI应用 =====

app = FastAPI(**APP_CONFIG)

# 添加CORS中间件
app.add_middleware(CORSMiddleware, **CORS_CONFIG)

# ===== 依赖注入 =====

def get_data_store() -> DataStore:
    """获取数据存储实例"""
    return data_store

# ===== 中间件 =====

@app.middleware("http")
async def log_requests(request, call_next):
    """请求日志中间件"""
    start_time = datetime.now()
    
    # 记录请求
    logger.info(f"Request: {request.method} {request.url}")
    
    # 处理请求
    response = await call_next(request)
    
    # 记录响应
    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"Response: {response.status_code} - {process_time:.3f}s")
    
    return response

# ===== 工具函数 =====

def create_success_response(message: str, data: Any = None) -> APIResponse:
    """创建成功响应"""
    return APIResponse(success=True, message=message, data=data)

def create_error_response(message: str, data: Any = None) -> APIResponse:
    """创建错误响应"""
    return APIResponse(success=False, message=message, data=data)

# ===== API路由 =====

@app.get("/", response_model=APIResponse)
async def root():
    """根路径 - API信息"""
    return create_success_response(
        message="Cube1 Group Backend API",
        data={
            "version": APP_CONFIG["version"],
            "description": APP_CONFIG["description"],
            "endpoints": {
                "health": "/health - 健康检查",
                "matrix": "/matrix - 矩阵数据管理",
                "word-library": "/word-library - 词库管理",
                "docs": "/docs - API文档",
            }
        }
    )

# ===== 健康检查 =====

@app.get("/health", response_model=APIResponse)
async def health_check():
    """健康检查"""
    return create_success_response(
        message="服务运行正常",
        data={
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": APP_CONFIG["version"],
            "uptime": "运行中"
        }
    )

# ===== 矩阵API =====

@app.get("/matrix", response_model=APIResponse)
async def get_matrix_data(store: DataStore = Depends(get_data_store)):
    """获取矩阵数据"""
    if not store.matrix_data:
        raise HTTPException(status_code=404, detail="矩阵数据未找到")
    
    return create_success_response(
        message="获取矩阵数据成功",
        data=store.matrix_data.dict()
    )

@app.post("/matrix", response_model=APIResponse)
async def update_matrix_data(
    matrix_data: MatrixData,
    store: DataStore = Depends(get_data_store)
):
    """更新矩阵数据"""
    try:
        matrix_data.last_updated = datetime.now()
        store.matrix_data = matrix_data
        
        return create_success_response(
            message="矩阵数据更新成功",
            data=matrix_data.dict()
        )
    except Exception as e:
        logger.error(f"更新矩阵数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@app.get("/matrix/config", response_model=APIResponse)
async def get_matrix_config(store: DataStore = Depends(get_data_store)):
    """获取矩阵配置"""
    return create_success_response(
        message="获取矩阵配置成功",
        data=store.matrix_config.dict()
    )

@app.post("/matrix/config", response_model=APIResponse)
async def update_matrix_config(
    config: MatrixConfig,
    store: DataStore = Depends(get_data_store)
):
    """更新矩阵配置"""
    try:
        store.matrix_config = config
        
        return create_success_response(
            message="矩阵配置更新成功",
            data=config.dict()
        )
    except Exception as e:
        logger.error(f"更新矩阵配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@app.get("/matrix/cell/{x}/{y}", response_model=APIResponse)
async def get_cell_data(
    x: int,
    y: int,
    store: DataStore = Depends(get_data_store)
):
    """获取单个单元格数据"""
    if not store.matrix_data:
        raise HTTPException(status_code=404, detail="矩阵数据未找到")
    
    # 查找单元格
    cell = next(
        (cell for cell in store.matrix_data.cells 
         if cell.coordinate.x == x and cell.coordinate.y == y),
        None
    )
    
    if not cell:
        raise HTTPException(status_code=404, detail=f"单元格 ({x}, {y}) 未找到")
    
    return create_success_response(
        message="获取单元格数据成功",
        data=cell.dict()
    )

@app.put("/matrix/cell/{x}/{y}", response_model=APIResponse)
async def update_cell_data(
    x: int,
    y: int,
    cell_data: CellData,
    store: DataStore = Depends(get_data_store)
):
    """更新单个单元格数据"""
    if not store.matrix_data:
        raise HTTPException(status_code=404, detail="矩阵数据未找到")
    
    # 查找并更新单元格
    for i, cell in enumerate(store.matrix_data.cells):
        if cell.coordinate.x == x and cell.coordinate.y == y:
            store.matrix_data.cells[i] = cell_data
            store.matrix_data.last_updated = datetime.now()
            
            return create_success_response(
                message="单元格数据更新成功",
                data=cell_data.dict()
            )
    
    raise HTTPException(status_code=404, detail=f"单元格 ({x}, {y}) 未找到")

# ===== 词库API =====

@app.get("/word-library", response_model=APIResponse)
async def get_word_libraries(store: DataStore = Depends(get_data_store)):
    """获取所有词库"""
    return create_success_response(
        message="获取词库列表成功",
        data=store.word_libraries
    )

@app.get("/word-library/{library_key}", response_model=APIResponse)
async def get_word_library(
    library_key: str,
    store: DataStore = Depends(get_data_store)
):
    """获取指定词库"""
    if library_key not in store.word_libraries:
        raise HTTPException(status_code=404, detail=f"词库 {library_key} 未找到")
    
    return create_success_response(
        message="获取词库成功",
        data=store.word_libraries[library_key].dict()
    )

@app.post("/word-library/{library_key}/words", response_model=APIResponse)
async def add_word_to_library(
    library_key: str,
    word: str = Query(..., description="要添加的词语"),
    store: DataStore = Depends(get_data_store)
):
    """向词库添加词语"""
    if library_key not in store.word_libraries:
        raise HTTPException(status_code=404, detail=f"词库 {library_key} 未找到")
    
    library = store.word_libraries[library_key]
    
    # 检查词语是否已存在
    if word in library.words:
        raise HTTPException(status_code=400, detail="词语已存在")
    
    # 添加词语
    library.words.append(word)
    
    return create_success_response(
        message="词语添加成功",
        data={"library_key": library_key, "word": word, "total_words": len(library.words)}
    )

@app.delete("/word-library/{library_key}/words/{word}", response_model=APIResponse)
async def remove_word_from_library(
    library_key: str,
    word: str,
    store: DataStore = Depends(get_data_store)
):
    """从词库移除词语"""
    if library_key not in store.word_libraries:
        raise HTTPException(status_code=404, detail=f"词库 {library_key} 未找到")
    
    library = store.word_libraries[library_key]
    
    # 检查词语是否存在
    if word not in library.words:
        raise HTTPException(status_code=404, detail="词语不存在")
    
    # 移除词语
    library.words.remove(word)
    
    return create_success_response(
        message="词语移除成功",
        data={"library_key": library_key, "word": word, "total_words": len(library.words)}
    )

@app.delete("/word-library/{library_key}/words", response_model=APIResponse)
async def clear_word_library(
    library_key: str,
    store: DataStore = Depends(get_data_store)
):
    """清空词库"""
    if library_key not in store.word_libraries:
        raise HTTPException(status_code=404, detail=f"词库 {library_key} 未找到")
    
    library = store.word_libraries[library_key]
    word_count = len(library.words)
    library.words.clear()
    
    return create_success_response(
        message="词库清空成功",
        data={"library_key": library_key, "cleared_words": word_count}
    )

# ===== 统计API =====

@app.get("/stats", response_model=APIResponse)
async def get_statistics(store: DataStore = Depends(get_data_store)):
    """获取系统统计信息"""
    matrix_stats = {
        "total_cells": len(store.matrix_data.cells) if store.matrix_data else 0,
        "active_cells": len([cell for cell in store.matrix_data.cells if cell.is_active]) if store.matrix_data else 0,
        "cells_with_words": len([cell for cell in store.matrix_data.cells if cell.word]) if store.matrix_data else 0,
    }
    
    word_library_stats = {
        "total_libraries": len(store.word_libraries),
        "total_words": sum(len(lib.words) for lib in store.word_libraries.values()),
        "empty_libraries": len([lib for lib in store.word_libraries.values() if not lib.words]),
    }
    
    return create_success_response(
        message="获取统计信息成功",
        data={
            "matrix": matrix_stats,
            "word_libraries": word_library_stats,
            "timestamp": datetime.now().isoformat()
        }
    )

# ===== 应用启动 =====

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
