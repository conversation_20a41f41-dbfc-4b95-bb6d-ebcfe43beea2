"""
数据模型定义 - 整合版本
🎯 核心价值：统一的数据模型定义，支持前后端数据交换
📦 功能范围：Pydantic模型、数据验证、序列化
🔄 架构设计：基于Pydantic的类型安全数据模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum

# ===== 枚举类型 =====

class ColorType(str, Enum):
    """颜色类型枚举"""
    RED = "red"
    BLUE = "blue"
    GREEN = "green"
    YELLOW = "yellow"
    PURPLE = "purple"
    ORANGE = "orange"
    PINK = "pink"
    CYAN = "cyan"
    BLACK = "black"
    WHITE = "white"
    GRAY = "gray"
    BROWN = "brown"

class DataLevel(int, Enum):
    """数据级别枚举"""
    LEVEL_1 = 1
    LEVEL_2 = 2
    LEVEL_3 = 3
    LEVEL_4 = 4

class BusinessMode(str, Enum):
    """业务模式枚举"""
    COORDINATE = "coordinate"
    COLOR = "color"
    LEVEL = "level"
    WORD = "word"
    COLOR_WORD = "color-word"
    NUMBER_WORD = "number-word"

class MainMode(str, Enum):
    """主模式枚举"""
    COLOR = "color"
    NUMBER = "number"

class ContentMode(str, Enum):
    """内容模式枚举"""
    WORD = "word"
    NUMBER = "number"
    COORDINATE = "coordinate"

# ===== 基础模型 =====

class Coordinate(BaseModel):
    """坐标模型"""
    x: int = Field(..., ge=0, le=32, description="X坐标 (0-32)")
    y: int = Field(..., ge=0, le=32, description="Y坐标 (0-32)")
    
    class Config:
        schema_extra = {
            "example": {"x": 16, "y": 16}
        }
    
    def __str__(self) -> str:
        return f"({self.x}, {self.y})"
    
    def __hash__(self) -> int:
        return hash((self.x, self.y))
    
    def to_key(self) -> str:
        """转换为字符串键"""
        return f"{self.x},{self.y}"
    
    @classmethod
    def from_key(cls, key: str) -> "Coordinate":
        """从字符串键创建坐标"""
        x, y = map(int, key.split(","))
        return cls(x=x, y=y)

class CellData(BaseModel):
    """单元格数据模型"""
    coordinate: Coordinate = Field(..., description="单元格坐标")
    color: ColorType = Field(ColorType.WHITE, description="单元格颜色")
    level: DataLevel = Field(DataLevel.LEVEL_1, description="数据级别")
    value: Optional[int] = Field(None, description="数值")
    word: Optional[str] = Field(None, max_length=10, description="词语")
    is_active: bool = Field(False, description="是否激活")
    group: Optional[str] = Field(None, max_length=20, description="分组名称")
    
    class Config:
        use_enum_values = True
        schema_extra = {
            "example": {
                "coordinate": {"x": 16, "y": 16},
                "color": "red",
                "level": 1,
                "value": 42,
                "word": "测试",
                "is_active": True,
                "group": "A组"
            }
        }
    
    @validator("word")
    def validate_word(cls, v):
        """验证词语格式"""
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
            if len(v) > 10:
                raise ValueError("词语长度不能超过10个字符")
            # 检查是否包含有效字符
            import re
            if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\s]+$', v):
                raise ValueError("词语只能包含中文、英文、数字和空格")
        return v
    
    @validator("value")
    def validate_value(cls, v):
        """验证数值范围"""
        if v is not None and (v < -999999 or v > 999999):
            raise ValueError("数值必须在 -999999 到 999999 之间")
        return v

class MatrixData(BaseModel):
    """矩阵数据模型"""
    cells: List[CellData] = Field(..., description="单元格数据列表")
    size: int = Field(33, ge=1, le=100, description="矩阵尺寸")
    version: str = Field("1.0.0", description="数据版本")
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    
    class Config:
        schema_extra = {
            "example": {
                "cells": [
                    {
                        "coordinate": {"x": 0, "y": 0},
                        "color": "white",
                        "level": 1,
                        "is_active": False
                    }
                ],
                "size": 33,
                "version": "1.0.0",
                "last_updated": "2025-01-01T00:00:00",
                "metadata": {"created_by": "system"}
            }
        }
    
    @validator("cells")
    def validate_cells(cls, v, values):
        """验证单元格数据"""
        if "size" in values:
            expected_count = values["size"] ** 2
            if len(v) != expected_count:
                raise ValueError(f"单元格数量应为 {expected_count}，实际为 {len(v)}")
        
        # 检查坐标唯一性
        coordinates = set()
        for cell in v:
            coord_key = cell.coordinate.to_key()
            if coord_key in coordinates:
                raise ValueError(f"重复的坐标: {cell.coordinate}")
            coordinates.add(coord_key)
        
        return v
    
    def get_cell(self, x: int, y: int) -> Optional[CellData]:
        """获取指定坐标的单元格"""
        for cell in self.cells:
            if cell.coordinate.x == x and cell.coordinate.y == y:
                return cell
        return None
    
    def get_active_cells(self) -> List[CellData]:
        """获取所有激活的单元格"""
        return [cell for cell in self.cells if cell.is_active]
    
    def get_cells_by_color(self, color: ColorType) -> List[CellData]:
        """获取指定颜色的单元格"""
        return [cell for cell in self.cells if cell.color == color]

class MatrixConfig(BaseModel):
    """矩阵配置模型"""
    mode: BusinessMode = Field(BusinessMode.COLOR_WORD, description="业务模式")
    main_mode: MainMode = Field(MainMode.COLOR, description="主模式")
    content_mode: ContentMode = Field(ContentMode.WORD, description="内容模式")
    is_color_mode: bool = Field(True, description="是否颜色模式")
    is_word_mode: bool = Field(True, description="是否词语模式")
    is_number_mode: bool = Field(False, description="是否数字模式")
    is_coordinate_mode: bool = Field(False, description="是否坐标模式")
    
    class Config:
        use_enum_values = True
        schema_extra = {
            "example": {
                "mode": "color-word",
                "main_mode": "color",
                "content_mode": "word",
                "is_color_mode": True,
                "is_word_mode": True,
                "is_number_mode": False,
                "is_coordinate_mode": False
            }
        }

# ===== 词库模型 =====

class WordLibrary(BaseModel):
    """词库模型"""
    key: str = Field(..., description="词库键 (格式: color-level)")
    words: List[str] = Field(default_factory=list, description="词语列表")
    display_name: str = Field(..., description="显示名称")
    background_color: str = Field(..., description="背景颜色 (十六进制)")
    text_color: str = Field(..., description="文字颜色 (十六进制)")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        schema_extra = {
            "example": {
                "key": "red-1",
                "words": ["苹果", "草莓", "樱桃"],
                "display_name": "红色1级",
                "background_color": "#ef4444",
                "text_color": "#000000",
                "created_at": "2025-01-01T00:00:00",
                "updated_at": "2025-01-01T00:00:00"
            }
        }
    
    @validator("key")
    def validate_key(cls, v):
        """验证词库键格式"""
        import re
        if not re.match(r'^[a-z]+-[1-4]$', v):
            raise ValueError("词库键格式应为: color-level (如: red-1)")
        return v
    
    @validator("words")
    def validate_words(cls, v):
        """验证词语列表"""
        # 去重
        unique_words = list(dict.fromkeys(v))
        
        # 验证每个词语
        for word in unique_words:
            if not word or not word.strip():
                continue
            if len(word.strip()) > 10:
                raise ValueError(f"词语 '{word}' 长度超过10个字符")
        
        return [word.strip() for word in unique_words if word.strip()]
    
    @validator("background_color", "text_color")
    def validate_color(cls, v):
        """验证颜色格式"""
        import re
        if not re.match(r'^#[0-9a-fA-F]{6}$', v):
            raise ValueError("颜色格式应为十六进制 (如: #ff0000)")
        return v.lower()
    
    def add_word(self, word: str) -> bool:
        """添加词语"""
        word = word.strip()
        if word and word not in self.words:
            self.words.append(word)
            self.updated_at = datetime.now()
            return True
        return False
    
    def remove_word(self, word: str) -> bool:
        """移除词语"""
        if word in self.words:
            self.words.remove(word)
            self.updated_at = datetime.now()
            return True
        return False
    
    def clear_words(self):
        """清空词语"""
        self.words.clear()
        self.updated_at = datetime.now()

# ===== 响应模型 =====

class APIResponse(BaseModel):
    """统一API响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    error_code: Optional[str] = Field(None, description="错误代码")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": {"result": "示例数据"},
                "timestamp": "2025-01-01T00:00:00",
                "error_code": None
            }
        }

class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[Any] = Field(..., description="数据项列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")
    
    class Config:
        schema_extra = {
            "example": {
                "items": [{"id": 1, "name": "示例"}],
                "total": 100,
                "page": 1,
                "page_size": 10,
                "total_pages": 10
            }
        }

# ===== 请求模型 =====

class CellUpdateRequest(BaseModel):
    """单元格更新请求模型"""
    color: Optional[ColorType] = Field(None, description="颜色")
    level: Optional[DataLevel] = Field(None, description="级别")
    value: Optional[int] = Field(None, description="数值")
    word: Optional[str] = Field(None, description="词语")
    is_active: Optional[bool] = Field(None, description="是否激活")
    group: Optional[str] = Field(None, description="分组")
    
    class Config:
        use_enum_values = True

class BatchCellUpdateRequest(BaseModel):
    """批量单元格更新请求模型"""
    updates: List[Dict[str, Any]] = Field(..., description="更新列表")
    
    class Config:
        schema_extra = {
            "example": {
                "updates": [
                    {
                        "coordinate": {"x": 0, "y": 0},
                        "color": "red",
                        "is_active": True
                    }
                ]
            }
        }

class WordLibraryCreateRequest(BaseModel):
    """词库创建请求模型"""
    key: str = Field(..., description="词库键")
    display_name: str = Field(..., description="显示名称")
    background_color: str = Field(..., description="背景颜色")
    text_color: str = Field(..., description="文字颜色")
    words: List[str] = Field(default_factory=list, description="初始词语列表")

class AddWordRequest(BaseModel):
    """添加词语请求模型"""
    word: str = Field(..., min_length=1, max_length=10, description="词语")
    
    @validator("word")
    def validate_word(cls, v):
        """验证词语"""
        v = v.strip()
        if not v:
            raise ValueError("词语不能为空")
        import re
        if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\s]+$', v):
            raise ValueError("词语只能包含中文、英文、数字和空格")
        return v

# ===== 统计模型 =====

class MatrixStatistics(BaseModel):
    """矩阵统计模型"""
    total_cells: int = Field(..., description="总单元格数")
    active_cells: int = Field(..., description="激活单元格数")
    cells_with_words: int = Field(..., description="有词语的单元格数")
    cells_with_values: int = Field(..., description="有数值的单元格数")
    color_distribution: Dict[str, int] = Field(..., description="颜色分布")
    level_distribution: Dict[str, int] = Field(..., description="级别分布")

class WordLibraryStatistics(BaseModel):
    """词库统计模型"""
    total_libraries: int = Field(..., description="总词库数")
    total_words: int = Field(..., description="总词语数")
    empty_libraries: int = Field(..., description="空词库数")
    average_words_per_library: float = Field(..., description="平均每个词库的词语数")
    largest_library_size: int = Field(..., description="最大词库大小")
    smallest_library_size: int = Field(..., description="最小词库大小")

class SystemStatistics(BaseModel):
    """系统统计模型"""
    matrix: MatrixStatistics = Field(..., description="矩阵统计")
    word_libraries: WordLibraryStatistics = Field(..., description="词库统计")
    timestamp: datetime = Field(default_factory=datetime.now, description="统计时间")
