"""
业务服务层 - 整合版本
🎯 核心价值：统一的业务逻辑处理，整合所有后端服务功能
📦 功能范围：数据处理、业务规则、缓存管理、文件操作
🔄 架构设计：基于服务类的业务逻辑封装
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from .models import (
    MatrixData, MatrixConfig, CellData, Coordinate,
    WordLibrary, ColorType, DataLevel,
    MatrixStatistics, WordLibraryStatistics, SystemStatistics
)

# 配置日志
logger = logging.getLogger(__name__)

# ===== 数据存储服务 =====

class DataStorageService:
    """数据存储服务"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 数据文件路径
        self.matrix_file = self.data_dir / "matrix_data.json"
        self.config_file = self.data_dir / "matrix_config.json"
        self.libraries_file = self.data_dir / "word_libraries.json"
        
        # 内存缓存
        self._matrix_data: Optional[MatrixData] = None
        self._matrix_config: Optional[MatrixConfig] = None
        self._word_libraries: Dict[str, WordLibrary] = {}
        
        # 加载数据
        self._load_all_data()
    
    def _load_all_data(self):
        """加载所有数据"""
        try:
            self._load_matrix_data()
            self._load_matrix_config()
            self._load_word_libraries()
            logger.info("数据加载完成")
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            self._initialize_default_data()
    
    def _load_matrix_data(self):
        """加载矩阵数据"""
        if self.matrix_file.exists():
            with open(self.matrix_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self._matrix_data = MatrixData(**data)
        else:
            self._matrix_data = self._create_default_matrix()
    
    def _load_matrix_config(self):
        """加载矩阵配置"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self._matrix_config = MatrixConfig(**data)
        else:
            self._matrix_config = MatrixConfig()
    
    def _load_word_libraries(self):
        """加载词库数据"""
        if self.libraries_file.exists():
            with open(self.libraries_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self._word_libraries = {
                    key: WordLibrary(**lib_data) 
                    for key, lib_data in data.items()
                }
        else:
            self._word_libraries = self._create_default_libraries()
    
    def _initialize_default_data(self):
        """初始化默认数据"""
        self._matrix_data = self._create_default_matrix()
        self._matrix_config = MatrixConfig()
        self._word_libraries = self._create_default_libraries()
        self.save_all_data()
    
    def _create_default_matrix(self) -> MatrixData:
        """创建默认矩阵数据"""
        cells = []
        for x in range(33):
            for y in range(33):
                cells.append(CellData(
                    coordinate=Coordinate(x=x, y=y),
                    color=ColorType.WHITE,
                    level=DataLevel.LEVEL_1,
                    is_active=False
                ))
        
        return MatrixData(cells=cells)
    
    def _create_default_libraries(self) -> Dict[str, WordLibrary]:
        """创建默认词库"""
        libraries = {}
        colors = ["red", "blue", "green", "yellow", "purple", "orange", "pink", "cyan"]
        levels = [1, 2, 3, 4]
        
        color_hex_map = {
            "red": "#ef4444", "blue": "#3b82f6", "green": "#22c55e",
            "yellow": "#eab308", "purple": "#a855f7", "orange": "#f97316",
            "pink": "#ec4899", "cyan": "#06b6d4"
        }
        
        for color in colors:
            for level in levels:
                key = f"{color}-{level}"
                libraries[key] = WordLibrary(
                    key=key,
                    words=[],
                    display_name=f"{color.title()}{level}级",
                    background_color=color_hex_map[color],
                    text_color="#000000" if color != "black" else "#ffffff"
                )
        
        return libraries
    
    def save_all_data(self):
        """保存所有数据"""
        try:
            self._save_matrix_data()
            self._save_matrix_config()
            self._save_word_libraries()
            logger.info("数据保存完成")
        except Exception as e:
            logger.error(f"数据保存失败: {e}")
            raise
    
    def _save_matrix_data(self):
        """保存矩阵数据"""
        if self._matrix_data:
            with open(self.matrix_file, 'w', encoding='utf-8') as f:
                json.dump(self._matrix_data.dict(), f, ensure_ascii=False, indent=2, default=str)
    
    def _save_matrix_config(self):
        """保存矩阵配置"""
        if self._matrix_config:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._matrix_config.dict(), f, ensure_ascii=False, indent=2)
    
    def _save_word_libraries(self):
        """保存词库数据"""
        data = {key: lib.dict() for key, lib in self._word_libraries.items()}
        with open(self.libraries_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
    
    # 公共接口
    def get_matrix_data(self) -> Optional[MatrixData]:
        """获取矩阵数据"""
        return self._matrix_data
    
    def set_matrix_data(self, data: MatrixData):
        """设置矩阵数据"""
        self._matrix_data = data
        self._save_matrix_data()
    
    def get_matrix_config(self) -> Optional[MatrixConfig]:
        """获取矩阵配置"""
        return self._matrix_config
    
    def set_matrix_config(self, config: MatrixConfig):
        """设置矩阵配置"""
        self._matrix_config = config
        self._save_matrix_config()
    
    def get_word_libraries(self) -> Dict[str, WordLibrary]:
        """获取所有词库"""
        return self._word_libraries.copy()
    
    def get_word_library(self, key: str) -> Optional[WordLibrary]:
        """获取指定词库"""
        return self._word_libraries.get(key)
    
    def set_word_library(self, key: str, library: WordLibrary):
        """设置词库"""
        self._word_libraries[key] = library
        self._save_word_libraries()
    
    def delete_word_library(self, key: str) -> bool:
        """删除词库"""
        if key in self._word_libraries:
            del self._word_libraries[key]
            self._save_word_libraries()
            return True
        return False

# ===== 矩阵服务 =====

class MatrixService:
    """矩阵业务服务"""
    
    def __init__(self, storage: DataStorageService):
        self.storage = storage
    
    def get_matrix_data(self) -> Optional[MatrixData]:
        """获取矩阵数据"""
        return self.storage.get_matrix_data()
    
    def update_matrix_data(self, data: MatrixData) -> MatrixData:
        """更新矩阵数据"""
        data.last_updated = datetime.now()
        self.storage.set_matrix_data(data)
        return data
    
    def get_cell_data(self, x: int, y: int) -> Optional[CellData]:
        """获取单元格数据"""
        matrix_data = self.storage.get_matrix_data()
        if not matrix_data:
            return None
        
        return matrix_data.get_cell(x, y)
    
    def update_cell_data(self, x: int, y: int, cell_data: CellData) -> bool:
        """更新单元格数据"""
        matrix_data = self.storage.get_matrix_data()
        if not matrix_data:
            return False
        
        # 查找并更新单元格
        for i, cell in enumerate(matrix_data.cells):
            if cell.coordinate.x == x and cell.coordinate.y == y:
                matrix_data.cells[i] = cell_data
                matrix_data.last_updated = datetime.now()
                self.storage.set_matrix_data(matrix_data)
                return True
        
        return False
    
    def batch_update_cells(self, updates: List[Tuple[int, int, CellData]]) -> int:
        """批量更新单元格"""
        matrix_data = self.storage.get_matrix_data()
        if not matrix_data:
            return 0
        
        updated_count = 0
        cell_map = {(cell.coordinate.x, cell.coordinate.y): i for i, cell in enumerate(matrix_data.cells)}
        
        for x, y, cell_data in updates:
            if (x, y) in cell_map:
                matrix_data.cells[cell_map[(x, y)]] = cell_data
                updated_count += 1
        
        if updated_count > 0:
            matrix_data.last_updated = datetime.now()
            self.storage.set_matrix_data(matrix_data)
        
        return updated_count
    
    def get_matrix_config(self) -> Optional[MatrixConfig]:
        """获取矩阵配置"""
        return self.storage.get_matrix_config()
    
    def update_matrix_config(self, config: MatrixConfig) -> MatrixConfig:
        """更新矩阵配置"""
        self.storage.set_matrix_config(config)
        return config
    
    def get_matrix_statistics(self) -> MatrixStatistics:
        """获取矩阵统计信息"""
        matrix_data = self.storage.get_matrix_data()
        if not matrix_data:
            return MatrixStatistics(
                total_cells=0, active_cells=0, cells_with_words=0,
                cells_with_values=0, color_distribution={}, level_distribution={}
            )
        
        # 统计数据
        total_cells = len(matrix_data.cells)
        active_cells = len([cell for cell in matrix_data.cells if cell.is_active])
        cells_with_words = len([cell for cell in matrix_data.cells if cell.word])
        cells_with_values = len([cell for cell in matrix_data.cells if cell.value is not None])
        
        # 颜色分布
        color_distribution = {}
        for cell in matrix_data.cells:
            color = cell.color.value if hasattr(cell.color, 'value') else str(cell.color)
            color_distribution[color] = color_distribution.get(color, 0) + 1
        
        # 级别分布
        level_distribution = {}
        for cell in matrix_data.cells:
            level = cell.level.value if hasattr(cell.level, 'value') else str(cell.level)
            level_distribution[str(level)] = level_distribution.get(str(level), 0) + 1
        
        return MatrixStatistics(
            total_cells=total_cells,
            active_cells=active_cells,
            cells_with_words=cells_with_words,
            cells_with_values=cells_with_values,
            color_distribution=color_distribution,
            level_distribution=level_distribution
        )

# ===== 词库服务 =====

class WordLibraryService:
    """词库业务服务"""
    
    def __init__(self, storage: DataStorageService):
        self.storage = storage
    
    def get_all_libraries(self) -> Dict[str, WordLibrary]:
        """获取所有词库"""
        return self.storage.get_word_libraries()
    
    def get_library(self, key: str) -> Optional[WordLibrary]:
        """获取指定词库"""
        return self.storage.get_word_library(key)
    
    def create_library(self, library: WordLibrary) -> WordLibrary:
        """创建词库"""
        self.storage.set_word_library(library.key, library)
        return library
    
    def add_word_to_library(self, library_key: str, word: str) -> bool:
        """向词库添加词语"""
        library = self.storage.get_word_library(library_key)
        if not library:
            return False
        
        if library.add_word(word):
            self.storage.set_word_library(library_key, library)
            return True
        return False
    
    def remove_word_from_library(self, library_key: str, word: str) -> bool:
        """从词库移除词语"""
        library = self.storage.get_word_library(library_key)
        if not library:
            return False
        
        if library.remove_word(word):
            self.storage.set_word_library(library_key, library)
            return True
        return False
    
    def clear_library(self, library_key: str) -> bool:
        """清空词库"""
        library = self.storage.get_word_library(library_key)
        if not library:
            return False
        
        library.clear_words()
        self.storage.set_word_library(library_key, library)
        return True
    
    def search_words(self, query: str) -> Dict[str, List[str]]:
        """搜索词语"""
        results = {}
        libraries = self.storage.get_word_libraries()
        
        for key, library in libraries.items():
            matched_words = [
                word for word in library.words 
                if query.lower() in word.lower()
            ]
            if matched_words:
                results[key] = matched_words
        
        return results
    
    def get_word_library_statistics(self) -> WordLibraryStatistics:
        """获取词库统计信息"""
        libraries = self.storage.get_word_libraries()
        
        total_libraries = len(libraries)
        total_words = sum(len(lib.words) for lib in libraries.values())
        empty_libraries = len([lib for lib in libraries.values() if not lib.words])
        
        word_counts = [len(lib.words) for lib in libraries.values()]
        average_words = total_words / total_libraries if total_libraries > 0 else 0
        largest_library = max(word_counts) if word_counts else 0
        smallest_library = min(word_counts) if word_counts else 0
        
        return WordLibraryStatistics(
            total_libraries=total_libraries,
            total_words=total_words,
            empty_libraries=empty_libraries,
            average_words_per_library=average_words,
            largest_library_size=largest_library,
            smallest_library_size=smallest_library
        )

# ===== 统计服务 =====

class StatisticsService:
    """统计服务"""
    
    def __init__(self, matrix_service: MatrixService, word_library_service: WordLibraryService):
        self.matrix_service = matrix_service
        self.word_library_service = word_library_service
    
    def get_system_statistics(self) -> SystemStatistics:
        """获取系统统计信息"""
        matrix_stats = self.matrix_service.get_matrix_statistics()
        word_library_stats = self.word_library_service.get_word_library_statistics()
        
        return SystemStatistics(
            matrix=matrix_stats,
            word_libraries=word_library_stats,
            timestamp=datetime.now()
        )

# ===== 服务工厂 =====

class ServiceFactory:
    """服务工厂"""
    
    def __init__(self, data_dir: str = "data"):
        self.storage = DataStorageService(data_dir)
        self.matrix_service = MatrixService(self.storage)
        self.word_library_service = WordLibraryService(self.storage)
        self.statistics_service = StatisticsService(self.matrix_service, self.word_library_service)
    
    def get_matrix_service(self) -> MatrixService:
        """获取矩阵服务"""
        return self.matrix_service
    
    def get_word_library_service(self) -> WordLibraryService:
        """获取词库服务"""
        return self.word_library_service
    
    def get_statistics_service(self) -> StatisticsService:
        """获取统计服务"""
        return self.statistics_service
    
    def get_storage_service(self) -> DataStorageService:
        """获取存储服务"""
        return self.storage

# 全局服务实例
service_factory = ServiceFactory()
