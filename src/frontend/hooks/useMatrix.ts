/**
 * 矩阵相关Hooks - 整合版本
 * 🎯 核心价值：整合所有矩阵相关的Hook逻辑，提供统一的矩阵操作接口
 * 📦 功能范围：矩阵数据、事件处理、样式计算、渲染优化
 * 🔄 架构设计：基于矩阵核心模块的React Hook封装
 */

import { useCallback, useEffect, useMemo, useRef } from 'react';
import type {
  Coordinate,
  CellData,
  MatrixConfig,
  EventHandlerOptions,
  StyleCalculationOptions,
} from '../../shared/types';
import { isValidCoordinate, debounce, throttle } from '../../shared/utils';
import { DEBOUNCE_DELAYS } from '../../shared/constants';
import { 
  matrixCore, 
  useMatrixStore, 
  useCellData, 
  useIsSelected, 
  useCellRenderData 
} from '../core/matrix';
import { useGlobalAppStore } from '../core/store';

// ===== 基础矩阵Hook =====

/**
 * 矩阵主Hook - 提供矩阵的核心功能
 */
export const useMatrix = () => {
  const matrixStore = useMatrixStore();
  const incrementInteractions = useGlobalAppStore(state => state.incrementInteractions);

  // 包装操作方法以添加交互计数
  const wrappedOperations = useMemo(() => ({
    selectCell: (coordinate: Coordinate) => {
      incrementInteractions();
      matrixStore.selectCell(coordinate);
    },
    
    updateCell: (coordinate: Coordinate, updates: Partial<CellData>) => {
      incrementInteractions();
      matrixStore.updateCell(coordinate, updates);
    },
    
    batchUpdateCells: (updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>) => {
      incrementInteractions();
      matrixStore.batchUpdateCells(updates);
    },
    
    updateConfig: (config: Partial<MatrixConfig>) => {
      incrementInteractions();
      matrixStore.updateConfig(config);
    },
  }), [matrixStore, incrementInteractions]);

  return {
    // 状态
    matrixData: matrixStore.matrixData,
    config: matrixStore.config,
    selectedCoordinate: matrixStore.selectedCoordinate,
    isLoading: matrixStore.isLoading,
    error: matrixStore.error,
    
    // 操作方法
    ...wrappedOperations,
    clearSelection: matrixStore.clearSelection,
    reset: matrixStore.reset,
    initialize: matrixStore.initialize,
  };
};

// ===== 单元格Hook =====

/**
 * 单元格Hook - 提供单个单元格的完整功能
 */
export const useMatrixCell = (coordinate: Coordinate) => {
  const cellData = useCellData(coordinate);
  const isSelected = useIsSelected(coordinate);
  const renderData = useCellRenderData(coordinate);
  const { selectCell, updateCell } = useMatrix();

  // 单元格操作方法
  const cellOperations = useMemo(() => ({
    select: () => selectCell(coordinate),
    update: (updates: Partial<CellData>) => updateCell(coordinate, updates),
    toggleActive: () => {
      if (cellData) {
        updateCell(coordinate, { isActive: !cellData.isActive });
      }
    },
    setColor: (color: CellData['color']) => updateCell(coordinate, { color }),
    setLevel: (level: CellData['level']) => updateCell(coordinate, { level }),
    setWord: (word: string) => updateCell(coordinate, { word }),
    setValue: (value: number) => updateCell(coordinate, { value }),
  }), [coordinate, cellData, selectCell, updateCell]);

  return {
    // 数据
    cellData,
    renderData,
    isSelected,
    coordinate,
    
    // 操作方法
    ...cellOperations,
  };
};

// ===== 事件处理Hook =====

/**
 * 矩阵事件处理Hook - 整合所有事件处理逻辑
 */
export const useMatrixEvents = (options: EventHandlerOptions = {}) => {
  const { selectCell, updateCell } = useMatrix();
  
  // 创建坐标点击处理器
  const createClickHandler = useCallback((
    onCellClick?: (coordinate: Coordinate, cellData?: CellData) => void
  ) => {
    return matrixCore.createEventHandler(
      (coordinate, event) => {
        selectCell(coordinate);
        
        if (onCellClick) {
          const cellData = matrixCore.getCellData(useMatrixStore.getState().matrixData, coordinate);
          onCellClick(coordinate, cellData);
        }
      },
      {
        enableDebounce: true,
        debounceDelay: DEBOUNCE_DELAYS.USER_INPUT,
        ...options,
      }
    );
  }, [selectCell, options]);

  // 创建键盘导航处理器
  const createKeyboardHandler = useCallback((
    currentCoordinate?: Coordinate
  ) => {
    return matrixCore.createKeyboardHandler(
      (direction) => {
        if (!currentCoordinate) return;
        
        let newCoordinate = { ...currentCoordinate };
        
        switch (direction) {
          case 'up':
            newCoordinate.y = Math.max(0, currentCoordinate.y - 1);
            break;
          case 'down':
            newCoordinate.y = Math.min(32, currentCoordinate.y + 1);
            break;
          case 'left':
            newCoordinate.x = Math.max(0, currentCoordinate.x - 1);
            break;
          case 'right':
            newCoordinate.x = Math.min(32, currentCoordinate.x + 1);
            break;
        }
        
        if (isValidCoordinate(newCoordinate)) {
          selectCell(newCoordinate);
        }
      },
      () => {
        // Enter键选择
        if (currentCoordinate) {
          const cellData = matrixCore.getCellData(useMatrixStore.getState().matrixData, currentCoordinate);
          if (cellData) {
            updateCell(currentCoordinate, { isActive: !cellData.isActive });
          }
        }
      },
      () => {
        // Escape键清除选择
        useMatrixStore.getState().clearSelection();
      }
    );
  }, [selectCell, updateCell]);

  return {
    createClickHandler,
    createKeyboardHandler,
  };
};

// ===== 样式计算Hook =====

/**
 * 矩阵样式Hook - 整合样式计算逻辑
 */
export const useMatrixCellStyle = (
  coordinate: Coordinate,
  options: Partial<StyleCalculationOptions> = {}
) => {
  const cellData = useCellData(coordinate);
  const isSelected = useIsSelected(coordinate);
  const config = useMatrixStore(state => state.config);

  // 计算样式
  const styleResult = useMemo(() => {
    if (!cellData) return { className: '', style: {} };

    return matrixCore.calculateCellStyle({
      coordinate,
      cellData,
      config,
      isEnhanced: isSelected,
      ...options,
    });
  }, [coordinate, cellData, config, isSelected, options]);

  return styleResult;
};

// ===== 性能优化Hook =====

/**
 * 矩阵渲染优化Hook - 整合渲染优化逻辑
 */
export const useMatrixRenderOptimization = () => {
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(0);

  // 渲染性能监控
  useEffect(() => {
    renderCountRef.current += 1;
    lastRenderTimeRef.current = performance.now();
  });

  // 防抖的重新渲染函数
  const debouncedRerender = useMemo(
    () => debounce(() => {
      // 触发重新渲染的逻辑
      useMatrixStore.getState().initialize();
    }, DEBOUNCE_DELAYS.SCROLL),
    []
  );

  // 节流的更新函数
  const throttledUpdate = useMemo(
    () => throttle((updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>) => {
      useMatrixStore.getState().batchUpdateCells(updates);
    }, 100),
    []
  );

  return {
    renderCount: renderCountRef.current,
    lastRenderTime: lastRenderTimeRef.current,
    debouncedRerender,
    throttledUpdate,
  };
};

// ===== 矩阵导航Hook =====

/**
 * 矩阵导航Hook - 提供矩阵导航功能
 */
export const useMatrixNavigation = () => {
  const selectedCoordinate = useMatrixStore(state => state.selectedCoordinate);
  const { selectCell } = useMatrix();

  const navigation = useMemo(() => ({
    // 移动到指定坐标
    moveTo: (coordinate: Coordinate) => {
      if (isValidCoordinate(coordinate)) {
        selectCell(coordinate);
      }
    },

    // 相对移动
    moveBy: (deltaX: number, deltaY: number) => {
      if (!selectedCoordinate) return;
      
      const newCoordinate = {
        x: Math.max(0, Math.min(32, selectedCoordinate.x + deltaX)),
        y: Math.max(0, Math.min(32, selectedCoordinate.y + deltaY)),
      };
      
      selectCell(newCoordinate);
    },

    // 移动到边界
    moveToEdge: (edge: 'top' | 'bottom' | 'left' | 'right') => {
      if (!selectedCoordinate) return;
      
      let newCoordinate = { ...selectedCoordinate };
      
      switch (edge) {
        case 'top':
          newCoordinate.y = 0;
          break;
        case 'bottom':
          newCoordinate.y = 32;
          break;
        case 'left':
          newCoordinate.x = 0;
          break;
        case 'right':
          newCoordinate.x = 32;
          break;
      }
      
      selectCell(newCoordinate);
    },

    // 移动到中心
    moveToCenter: () => {
      selectCell({ x: 16, y: 16 });
    },
  }), [selectedCoordinate, selectCell]);

  return {
    selectedCoordinate,
    ...navigation,
  };
};

// ===== 矩阵数据Hook =====

/**
 * 矩阵数据Hook - 提供矩阵数据的便捷访问
 */
export const useMatrixData = () => {
  const matrixData = useMatrixStore(state => state.matrixData);

  const dataHelpers = useMemo(() => ({
    // 获取所有激活的单元格
    getActiveCells: () => matrixData.cells.filter(cell => cell.isActive),
    
    // 按颜色分组
    getCellsByColor: (color: CellData['color']) => 
      matrixData.cells.filter(cell => cell.color === color),
    
    // 按级别分组
    getCellsByLevel: (level: CellData['level']) => 
      matrixData.cells.filter(cell => cell.level === level),
    
    // 获取有词语的单元格
    getCellsWithWords: () => 
      matrixData.cells.filter(cell => cell.word && cell.word.trim().length > 0),
    
    // 获取指定区域的单元格
    getCellsInRegion: (startCoord: Coordinate, endCoord: Coordinate) => {
      const minX = Math.min(startCoord.x, endCoord.x);
      const maxX = Math.max(startCoord.x, endCoord.x);
      const minY = Math.min(startCoord.y, endCoord.y);
      const maxY = Math.max(startCoord.y, endCoord.y);
      
      return matrixData.cells.filter(cell => 
        cell.coordinate.x >= minX && cell.coordinate.x <= maxX &&
        cell.coordinate.y >= minY && cell.coordinate.y <= maxY
      );
    },
  }), [matrixData]);

  return {
    matrixData,
    ...dataHelpers,
  };
};
