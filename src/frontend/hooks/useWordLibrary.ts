/**
 * 词库相关Hooks - 整合版本
 * 🎯 核心价值：整合所有词库相关的Hook逻辑，提供统一的词库操作接口
 * 📦 功能范围：词库管理、词语验证、输入处理、滚动管理
 * 🔄 架构设计：基于词库核心模块的React Hook封装
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type {
  WordLibrary,
  WordLibraryKey,
  WordValidationResult,
  BasicColorType,
  DataLevel,
} from '../../shared/types';
import { 
  createWordLibraryKey, 
  parseWordLibraryKey, 
  debounce, 
  unique 
} from '../../shared/utils';
import { DEBOUNCE_DELAYS, PRIMARY_COLORS, DATA_LEVELS } from '../../shared/constants';
import {
  wordLibraryCore,
  useWordLibraryStore,
  useWordLibrary,
  useSelectedWordLibrary,
  useWordLibraryStats,
  useNonEmptyLibraries,
} from '../core/wordLibrary';
import { useGlobalAppStore } from '../core/store';

// ===== 基础词库Hook =====

/**
 * 词库主Hook - 提供词库的核心功能
 */
export const useWordLibraries = () => {
  const wordLibraryStore = useWordLibraryStore();
  const incrementInteractions = useGlobalAppStore(state => state.incrementInteractions);

  // 包装操作方法以添加交互计数
  const wrappedOperations = useMemo(() => ({
    selectLibrary: (key: WordLibraryKey) => {
      incrementInteractions();
      wordLibraryStore.selectLibrary(key);
    },
    
    addWord: (key: WordLibraryKey, word: string) => {
      incrementInteractions();
      wordLibraryStore.addWord(key, word);
    },
    
    removeWord: (key: WordLibraryKey, word: string) => {
      incrementInteractions();
      wordLibraryStore.removeWord(key, word);
    },
    
    setWords: (key: WordLibraryKey, words: string[]) => {
      incrementInteractions();
      wordLibraryStore.setWords(key, words);
    },
    
    clearLibrary: (key: WordLibraryKey) => {
      incrementInteractions();
      wordLibraryStore.clearLibrary(key);
    },
  }), [wordLibraryStore, incrementInteractions]);

  return {
    // 状态
    libraries: wordLibraryStore.libraries,
    selectedLibraryKey: wordLibraryStore.selectedLibraryKey,
    temporaryWord: wordLibraryStore.temporaryWord,
    isEditMode: wordLibraryStore.isEditMode,
    isLoading: wordLibraryStore.isLoading,
    error: wordLibraryStore.error,
    
    // 操作方法
    ...wrappedOperations,
    setTemporaryWord: wordLibraryStore.setTemporaryWord,
    toggleEditMode: wordLibraryStore.toggleEditMode,
    clearSelection: wordLibraryStore.clearSelection,
    reset: wordLibraryStore.reset,
    initialize: wordLibraryStore.initialize,
  };
};

// ===== 单个词库Hook =====

/**
 * 单个词库Hook - 提供单个词库的完整功能
 */
export const useWordLibraryManager = (key: WordLibraryKey) => {
  const library = useWordLibrary(key);
  const { addWord, removeWord, setWords, clearLibrary } = useWordLibraries();
  const [localInput, setLocalInput] = useState('');

  // 词库操作方法
  const libraryOperations = useMemo(() => ({
    addWord: (word: string) => addWord(key, word),
    removeWord: (word: string) => removeWord(key, word),
    setWords: (words: string[]) => setWords(key, words),
    clearLibrary: () => clearLibrary(key),
    
    // 批量添加词语
    addWords: (words: string[]) => {
      const currentWords = library?.words || [];
      const newWords = unique([...currentWords, ...words]);
      setWords(key, newWords);
    },
    
    // 从文本添加词语
    addWordsFromText: (text: string, separator: string = '\n') => {
      const words = text
        .split(separator)
        .map(word => word.trim())
        .filter(word => word.length > 0);
      
      libraryOperations.addWords(words);
    },
    
    // 导出词语为文本
    exportToText: (separator: string = '\n') => {
      return library?.words.join(separator) || '';
    },
  }), [key, library, addWord, removeWord, setWords, clearLibrary]);

  return {
    // 数据
    library,
    key,
    localInput,
    setLocalInput,
    
    // 操作方法
    ...libraryOperations,
  };
};

// ===== 词语验证Hook =====

/**
 * 词语验证Hook - 整合词语验证逻辑
 */
export const useWordValidation = () => {
  const [validationCache, setValidationCache] = useState<Map<string, WordValidationResult>>(new Map());

  // 验证单个词语
  const validateWord = useCallback((word: string): WordValidationResult => {
    // 检查缓存
    if (validationCache.has(word)) {
      return validationCache.get(word)!;
    }

    const result = wordLibraryCore.validateWord(word);
    
    // 更新缓存
    setValidationCache(prev => new Map(prev).set(word, result));
    
    return result;
  }, [validationCache]);

  // 批量验证词语
  const validateWords = useCallback((words: string[]): Record<string, WordValidationResult> => {
    const results: Record<string, WordValidationResult> = {};
    
    words.forEach(word => {
      results[word] = validateWord(word);
    });
    
    return results;
  }, [validateWord]);

  // 验证重复词语
  const validateDuplicates = useCallback((words: string[]) => {
    return wordLibraryCore.validateDuplicates(words);
  }, []);

  // 清除验证缓存
  const clearValidationCache = useCallback(() => {
    setValidationCache(new Map());
  }, []);

  return {
    validateWord,
    validateWords,
    validateDuplicates,
    clearValidationCache,
    validationCacheSize: validationCache.size,
  };
};

// ===== 词库输入Hook =====

/**
 * 词库输入Hook - 整合输入处理逻辑
 */
export const useWordLibraryInput = (libraryKey: WordLibraryKey) => {
  const { addWord } = useWordLibraries();
  const { validateWord } = useWordValidation();
  const [inputValue, setInputValue] = useState('');
  const [validationResult, setValidationResult] = useState<WordValidationResult | null>(null);

  // 防抖的验证函数
  const debouncedValidate = useMemo(
    () => debounce((word: string) => {
      if (word.trim()) {
        const result = validateWord(word.trim());
        setValidationResult(result);
      } else {
        setValidationResult(null);
      }
    }, DEBOUNCE_DELAYS.USER_INPUT),
    [validateWord]
  );

  // 输入变化处理
  const handleInputChange = useCallback((value: string) => {
    setInputValue(value);
    debouncedValidate(value);
  }, [debouncedValidate]);

  // 提交词语
  const submitWord = useCallback(() => {
    const trimmedWord = inputValue.trim();
    
    if (!trimmedWord) return false;
    
    const validation = validateWord(trimmedWord);
    
    if (validation.isValid) {
      addWord(libraryKey, trimmedWord);
      setInputValue('');
      setValidationResult(null);
      return true;
    } else {
      setValidationResult(validation);
      return false;
    }
  }, [inputValue, validateWord, addWord, libraryKey]);

  // 键盘事件处理
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      submitWord();
    } else if (event.key === 'Escape') {
      setInputValue('');
      setValidationResult(null);
    }
  }, [submitWord]);

  return {
    inputValue,
    validationResult,
    isValid: validationResult?.isValid ?? true,
    handleInputChange,
    handleKeyDown,
    submitWord,
    clearInput: () => {
      setInputValue('');
      setValidationResult(null);
    },
  };
};

// ===== 词库滚动Hook =====

/**
 * 词库滚动Hook - 整合滚动管理逻辑
 */
export const useWordLibraryScroll = (containerRef: React.RefObject<HTMLElement>) => {
  const [scrollPosition, setScrollPosition] = useState({ top: 0, left: 0 });
  const [isScrolling, setIsScrolling] = useState(false);

  // 防抖的滚动结束处理
  const debouncedScrollEnd = useMemo(
    () => debounce(() => setIsScrolling(false), 150),
    []
  );

  // 滚动事件处理
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;
    
    const { scrollTop, scrollLeft } = containerRef.current;
    setScrollPosition({ top: scrollTop, left: scrollLeft });
    setIsScrolling(true);
    debouncedScrollEnd();
  }, [containerRef, debouncedScrollEnd]);

  // 滚动到指定位置
  const scrollTo = useCallback((options: { top?: number; left?: number; behavior?: ScrollBehavior }) => {
    if (!containerRef.current) return;
    
    containerRef.current.scrollTo({
      top: options.top,
      left: options.left,
      behavior: options.behavior || 'smooth',
    });
  }, [containerRef]);

  // 滚动到顶部
  const scrollToTop = useCallback(() => {
    scrollTo({ top: 0 });
  }, [scrollTo]);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (!containerRef.current) return;
    scrollTo({ top: containerRef.current.scrollHeight });
  }, [containerRef, scrollTo]);

  // 绑定滚动事件
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [containerRef, handleScroll]);

  return {
    scrollPosition,
    isScrolling,
    scrollTo,
    scrollToTop,
    scrollToBottom,
  };
};

// ===== 词库统计Hook =====

/**
 * 词库统计Hook - 提供词库统计信息
 */
export const useWordLibraryStatistics = () => {
  const stats = useWordLibraryStats();
  const nonEmptyLibraries = useNonEmptyLibraries();

  const extendedStats = useMemo(() => ({
    ...stats,
    // 非空词库数量
    nonEmptyLibrariesCount: Object.keys(nonEmptyLibraries).length,
    
    // 最大词库大小
    maxLibrarySize: Math.max(...Object.values(stats.byColor)),
    
    // 最小词库大小
    minLibrarySize: Math.min(...Object.values(stats.byColor)),
    
    // 词库填充率
    fillRate: stats.totalLibraries > 0 ? 
      (stats.totalLibraries - stats.emptyLibraries) / stats.totalLibraries : 0,
    
    // 按颜色排序的统计
    colorStatsSorted: PRIMARY_COLORS
      .map(color => ({ color, count: stats.byColor[color] }))
      .sort((a, b) => b.count - a.count),
    
    // 按级别排序的统计
    levelStatsSorted: DATA_LEVELS
      .map(level => ({ level, count: stats.byLevel[level] }))
      .sort((a, b) => b.count - a.count),
  }), [stats, nonEmptyLibraries]);

  return extendedStats;
};

// ===== 词库搜索Hook =====

/**
 * 词库搜索Hook - 提供词库搜索功能
 */
export const useWordLibrarySearch = () => {
  const { libraries } = useWordLibraries();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<{
    libraries: Array<{ key: WordLibraryKey; library: WordLibrary; matchedWords: string[] }>;
    totalMatches: number;
  }>({ libraries: [], totalMatches: 0 });

  // 防抖的搜索函数
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      if (!query.trim()) {
        setSearchResults({ libraries: [], totalMatches: 0 });
        return;
      }

      const results: typeof searchResults.libraries = [];
      let totalMatches = 0;

      Object.entries(libraries).forEach(([key, library]) => {
        const matchedWords = library.words.filter(word =>
          word.toLowerCase().includes(query.toLowerCase())
        );

        if (matchedWords.length > 0) {
          results.push({
            key: key as WordLibraryKey,
            library,
            matchedWords,
          });
          totalMatches += matchedWords.length;
        }
      });

      setSearchResults({ libraries: results, totalMatches });
    }, DEBOUNCE_DELAYS.SEARCH),
    [libraries]
  );

  // 搜索处理
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    debouncedSearch(query);
  }, [debouncedSearch]);

  // 清除搜索
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResults({ libraries: [], totalMatches: 0 });
  }, []);

  return {
    searchQuery,
    searchResults,
    handleSearch,
    clearSearch,
    hasResults: searchResults.totalMatches > 0,
  };
};
