/**
 * 统一应用状态管理
 * 🎯 核心价值：整合所有应用状态，提供统一的状态管理接口
 * 📦 功能范围：全局状态、UI状态、用户偏好、应用配置
 * 🔄 架构设计：基于Zustand的分层状态管理，支持持久化和中间件
 */

import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { enableMapSet } from 'immer';
import type { AppState } from '../../shared/types';
import { STORAGE_KEYS, DEFAULT_MATRIX_CONFIG } from '../../shared/constants';
import { matrixCore } from './matrix';
import { wordLibraryCore } from './wordLibrary';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== UI状态管理 =====

interface UIState {
  /** 是否显示侧边栏 */
  showSidebar: boolean;
  /** 是否显示词库管理器 */
  showWordLibraryManager: boolean;
  /** 是否显示控制面板 */
  showControlPanel: boolean;
  /** 当前主题 */
  theme: 'light' | 'dark' | 'auto';
  /** 是否紧凑模式 */
  isCompactMode: boolean;
  /** 是否显示网格线 */
  showGridLines: boolean;
  /** 是否显示坐标 */
  showCoordinates: boolean;
  /** 缩放级别 */
  zoomLevel: number;
  /** 当前视口 */
  viewport: {
    width: number;
    height: number;
  };
  /** 通知列表 */
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: number;
    autoClose?: boolean;
  }>;

  // UI操作方法
  toggleSidebar: () => void;
  toggleWordLibraryManager: () => void;
  toggleControlPanel: () => void;
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  toggleCompactMode: () => void;
  toggleGridLines: () => void;
  toggleCoordinates: () => void;
  setZoomLevel: (level: number) => void;
  updateViewport: (width: number, height: number) => void;
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // 初始状态
      showSidebar: true,
      showWordLibraryManager: true,
      showControlPanel: true,
      theme: 'auto',
      isCompactMode: false,
      showGridLines: true,
      showCoordinates: false,
      zoomLevel: 1,
      viewport: { width: 1200, height: 800 },
      notifications: [],

      // UI操作方法
      toggleSidebar: () => set(state => ({ showSidebar: !state.showSidebar })),
      
      toggleWordLibraryManager: () => set(state => ({ 
        showWordLibraryManager: !state.showWordLibraryManager 
      })),
      
      toggleControlPanel: () => set(state => ({ 
        showControlPanel: !state.showControlPanel 
      })),
      
      setTheme: (theme) => set({ theme }),
      
      toggleCompactMode: () => set(state => ({ isCompactMode: !state.isCompactMode })),
      
      toggleGridLines: () => set(state => ({ showGridLines: !state.showGridLines })),
      
      toggleCoordinates: () => set(state => ({ showCoordinates: !state.showCoordinates })),
      
      setZoomLevel: (level) => {
        const clampedLevel = Math.max(0.5, Math.min(3, level));
        set({ zoomLevel: clampedLevel });
      },
      
      updateViewport: (width, height) => set({ viewport: { width, height } }),
      
      addNotification: (notification) => {
        const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newNotification = {
          ...notification,
          id,
          timestamp: Date.now(),
        };
        
        set(state => ({
          notifications: [...state.notifications, newNotification]
        }));

        // 自动移除通知
        if (notification.autoClose !== false) {
          setTimeout(() => {
            get().removeNotification(id);
          }, 5000);
        }
      },
      
      removeNotification: (id) => set(state => ({
        notifications: state.notifications.filter(n => n.id !== id)
      })),
      
      clearNotifications: () => set({ notifications: [] }),
    }),
    {
      name: STORAGE_KEYS.USER_PREFERENCES,
      partialize: (state) => ({
        showSidebar: state.showSidebar,
        showWordLibraryManager: state.showWordLibraryManager,
        showControlPanel: state.showControlPanel,
        theme: state.theme,
        isCompactMode: state.isCompactMode,
        showGridLines: state.showGridLines,
        showCoordinates: state.showCoordinates,
        zoomLevel: state.zoomLevel,
      }),
    }
  )
);

// ===== 应用配置状态 =====

interface AppConfigState {
  /** 应用版本 */
  version: string;
  /** 是否首次启动 */
  isFirstLaunch: boolean;
  /** 调试模式 */
  debugMode: boolean;
  /** 性能监控 */
  performanceMonitoring: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval: number;
  /** 最大撤销步数 */
  maxUndoSteps: number;
  /** 语言设置 */
  language: 'zh-CN' | 'en-US';
  /** 实验性功能 */
  experimentalFeatures: {
    virtualScrolling: boolean;
    webWorkers: boolean;
    offlineMode: boolean;
  };

  // 配置操作方法
  setFirstLaunch: (isFirst: boolean) => void;
  toggleDebugMode: () => void;
  togglePerformanceMonitoring: () => void;
  setAutoSaveInterval: (interval: number) => void;
  setMaxUndoSteps: (steps: number) => void;
  setLanguage: (language: 'zh-CN' | 'en-US') => void;
  toggleExperimentalFeature: (feature: keyof AppConfigState['experimentalFeatures']) => void;
  resetToDefaults: () => void;
}

export const useAppConfigStore = create<AppConfigState>()(
  persist(
    (set) => ({
      // 初始状态
      version: '1.0.0',
      isFirstLaunch: true,
      debugMode: false,
      performanceMonitoring: false,
      autoSaveInterval: 30000, // 30秒
      maxUndoSteps: 50,
      language: 'zh-CN',
      experimentalFeatures: {
        virtualScrolling: false,
        webWorkers: false,
        offlineMode: false,
      },

      // 配置操作方法
      setFirstLaunch: (isFirst) => set({ isFirstLaunch: isFirst }),
      
      toggleDebugMode: () => set(state => ({ debugMode: !state.debugMode })),
      
      togglePerformanceMonitoring: () => set(state => ({ 
        performanceMonitoring: !state.performanceMonitoring 
      })),
      
      setAutoSaveInterval: (interval) => {
        const clampedInterval = Math.max(5000, Math.min(300000, interval)); // 5秒到5分钟
        set({ autoSaveInterval: clampedInterval });
      },
      
      setMaxUndoSteps: (steps) => {
        const clampedSteps = Math.max(10, Math.min(100, steps)); // 10到100步
        set({ maxUndoSteps: clampedSteps });
      },
      
      setLanguage: (language) => set({ language }),
      
      toggleExperimentalFeature: (feature) => set(state => ({
        experimentalFeatures: {
          ...state.experimentalFeatures,
          [feature]: !state.experimentalFeatures[feature],
        }
      })),
      
      resetToDefaults: () => set({
        debugMode: false,
        performanceMonitoring: false,
        autoSaveInterval: 30000,
        maxUndoSteps: 50,
        language: 'zh-CN',
        experimentalFeatures: {
          virtualScrolling: false,
          webWorkers: false,
          offlineMode: false,
        },
      }),
    }),
    {
      name: 'cube1_app_config',
    }
  )
);

// ===== 全局应用状态 =====

interface GlobalAppState {
  /** 应用是否已初始化 */
  isInitialized: boolean;
  /** 是否处于加载状态 */
  isLoading: boolean;
  /** 全局错误信息 */
  globalError?: string;
  /** 当前活跃的功能模块 */
  activeModule: 'matrix' | 'wordLibrary' | 'settings';
  /** 应用统计信息 */
  stats: {
    sessionStartTime: number;
    totalInteractions: number;
    lastSaveTime?: number;
  };

  // 全局操作方法
  initialize: () => Promise<void>;
  setActiveModule: (module: GlobalAppState['activeModule']) => void;
  setGlobalError: (error?: string) => void;
  incrementInteractions: () => void;
  updateLastSaveTime: () => void;
  reset: () => void;
}

export const useGlobalAppStore = create<GlobalAppState>()(
  subscribeWithSelector(
    (set, get) => ({
      // 初始状态
      isInitialized: false,
      isLoading: false,
      globalError: undefined,
      activeModule: 'matrix',
      stats: {
        sessionStartTime: Date.now(),
        totalInteractions: 0,
        lastSaveTime: undefined,
      },

      // 全局操作方法
      initialize: async () => {
        set({ isLoading: true });
        
        try {
          // 初始化核心模块
          matrixCore.initialize();
          wordLibraryCore.initialize();
          
          set({ 
            isInitialized: true, 
            isLoading: false,
            globalError: undefined,
          });
        } catch (error) {
          set({ 
            isLoading: false,
            globalError: error instanceof Error ? error.message : '初始化失败',
          });
        }
      },

      setActiveModule: (module) => set({ activeModule: module }),
      
      setGlobalError: (error) => set({ globalError: error }),
      
      incrementInteractions: () => set(state => ({
        stats: {
          ...state.stats,
          totalInteractions: state.stats.totalInteractions + 1,
        }
      })),
      
      updateLastSaveTime: () => set(state => ({
        stats: {
          ...state.stats,
          lastSaveTime: Date.now(),
        }
      })),
      
      reset: () => set({
        isInitialized: false,
        isLoading: false,
        globalError: undefined,
        activeModule: 'matrix',
        stats: {
          sessionStartTime: Date.now(),
          totalInteractions: 0,
          lastSaveTime: undefined,
        },
      }),
    })
  )
);

// ===== 便捷选择器和Hook =====

/** 获取应用是否准备就绪 */
export const useAppReady = () => {
  return useGlobalAppStore(state => state.isInitialized && !state.isLoading);
};

/** 获取当前主题 */
export const useCurrentTheme = () => {
  const theme = useUIStore(state => state.theme);
  
  if (theme === 'auto') {
    // 检测系统主题
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  }
  
  return theme;
};

/** 获取响应式断点 */
export const useBreakpoint = () => {
  const viewport = useUIStore(state => state.viewport);
  
  if (viewport.width >= 1280) return 'xl';
  if (viewport.width >= 1024) return 'lg';
  if (viewport.width >= 768) return 'md';
  if (viewport.width >= 640) return 'sm';
  return 'xs';
};
