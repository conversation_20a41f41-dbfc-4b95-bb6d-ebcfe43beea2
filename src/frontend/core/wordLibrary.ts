/**
 * 词库核心模块 - 整合版本
 * 🎯 核心价值：统一的词库处理引擎，整合所有词库相关逻辑
 * 📦 功能范围：词库管理、词语验证、数据持久化
 * 🔄 架构设计：单一职责的核心类，集成所有词库功能
 */

import { produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  COLOR_HEX_VALUES,
  DATA_LEVELS,
  PRIMARY_COLORS,
  STORAGE_KEYS,
  WORD_VALIDATION
} from '../../shared/constants';
import type {
  BasicColorType,
  DataLevel,
  WordLibrary,
  WordLibraryKey,
  WordValidationResult,
} from '../../shared/types';
import {
  createWordLibraryKey,
  getWordLibraryDisplayName,
  parseWordLibraryKey,
  unique,
  validateWord
} from '../../shared/utils';

// ===== 词语验证引擎 =====

class WordValidationEngine {
  private validationCache = new Map<string, WordValidationResult>();

  /** 验证单个词语 */
  validateSingleWord(word: string): WordValidationResult {
    // 检查缓存
    if (this.validationCache.has(word)) {
      return this.validationCache.get(word)!;
    }

    const result: WordValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    // 基础验证
    const basicValidation = validateWord(word);
    if (!basicValidation.isValid) {
      result.isValid = false;
      result.errors.push(...basicValidation.errors);
    }

    // 长度验证
    if (word.length < WORD_VALIDATION.MIN_LENGTH) {
      result.isValid = false;
      result.errors.push(`词语长度不能少于${WORD_VALIDATION.MIN_LENGTH}个字符`);
    }

    if (word.length > WORD_VALIDATION.MAX_LENGTH) {
      result.isValid = false;
      result.errors.push(`词语长度不能超过${WORD_VALIDATION.MAX_LENGTH}个字符`);
    }

    // 字符验证
    if (!WORD_VALIDATION.ALLOWED_CHARS.test(word)) {
      result.isValid = false;
      result.errors.push('词语只能包含中文、英文、数字和空格');
    }

    // 禁止词语验证
    if (WORD_VALIDATION.FORBIDDEN_WORDS.includes(word.toLowerCase())) {
      result.isValid = false;
      result.errors.push('该词语不被允许');
    }

    // 重复空格警告
    if (/\s{2,}/.test(word)) {
      result.warnings.push('词语包含多个连续空格');
      result.suggestions.push('建议移除多余的空格');
    }

    // 首尾空格警告
    if (word !== word.trim()) {
      result.warnings.push('词语首尾包含空格');
      result.suggestions.push('建议移除首尾空格');
    }

    // 缓存结果
    this.validationCache.set(word, result);

    return result;
  }

  /** 批量验证词语 */
  validateBatchWords(words: string[]): Record<string, WordValidationResult> {
    const results: Record<string, WordValidationResult> = {};

    words.forEach(word => {
      results[word] = this.validateSingleWord(word);
    });

    return results;
  }

  /** 验证词库中的重复词语 */
  validateDuplicates(words: string[]): { duplicates: string[]; unique: string[] } {
    const seen = new Set<string>();
    const duplicates: string[] = [];
    const uniqueWords: string[] = [];

    words.forEach(word => {
      const normalizedWord = word.trim().toLowerCase();
      if (seen.has(normalizedWord)) {
        if (!duplicates.includes(word)) {
          duplicates.push(word);
        }
      } else {
        seen.add(normalizedWord);
        uniqueWords.push(word);
      }
    });

    return { duplicates, unique: uniqueWords };
  }

  /** 清除验证缓存 */
  clearValidationCache(): void {
    this.validationCache.clear();
  }
}

// ===== 词库数据管理器 =====

class WordLibraryDataManager {
  /** 创建默认词库 */
  createDefaultWordLibrary(key: WordLibraryKey): WordLibrary {
    const { color, level } = parseWordLibraryKey(key);

    return {
      key,
      words: [],
      displayName: getWordLibraryDisplayName(key),
      backgroundColor: COLOR_HEX_VALUES[color],
      textColor: color === 'black' ? '#ffffff' : '#000000',
    };
  }

  /** 初始化所有词库 */
  initializeWordLibraries(): Record<WordLibraryKey, WordLibrary> {
    const libraries: Record<WordLibraryKey, WordLibrary> = {};

    PRIMARY_COLORS.forEach(color => {
      DATA_LEVELS.forEach(level => {
        const key = createWordLibraryKey(color, level);
        libraries[key] = this.createDefaultWordLibrary(key);
      });
    });

    return libraries;
  }

  /** 添加词语到词库 */
  addWordToLibrary(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey,
    word: string
  ): Record<WordLibraryKey, WordLibrary> {
    return produce(libraries, draft => {
      if (!draft[key]) {
        draft[key] = this.createDefaultWordLibrary(key);
      }

      const trimmedWord = word.trim();
      if (trimmedWord && !draft[key].words.includes(trimmedWord)) {
        draft[key].words.push(trimmedWord);
      }
    });
  }

  /** 从词库移除词语 */
  removeWordFromLibrary(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey,
    word: string
  ): Record<WordLibraryKey, WordLibrary> {
    return produce(libraries, draft => {
      if (draft[key]) {
        draft[key].words = draft[key].words.filter(w => w !== word);
      }
    });
  }

  /** 批量设置词库词语 */
  setLibraryWords(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey,
    words: string[]
  ): Record<WordLibraryKey, WordLibrary> {
    return produce(libraries, draft => {
      if (!draft[key]) {
        draft[key] = this.createDefaultWordLibrary(key);
      }

      // 去重并过滤空词语
      const uniqueWords = unique(words.map(w => w.trim()).filter(w => w.length > 0));
      draft[key].words = uniqueWords;
    });
  }

  /** 清空词库 */
  clearLibrary(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey
  ): Record<WordLibraryKey, WordLibrary> {
    return produce(libraries, draft => {
      if (draft[key]) {
        draft[key].words = [];
      }
    });
  }

  /** 获取词库统计信息 */
  getLibraryStats(libraries: Record<WordLibraryKey, WordLibrary>) {
    const stats = {
      totalLibraries: 0,
      totalWords: 0,
      averageWordsPerLibrary: 0,
      emptyLibraries: 0,
      byColor: {} as Record<BasicColorType, number>,
      byLevel: {} as Record<DataLevel, number>,
    };

    // 初始化计数器
    PRIMARY_COLORS.forEach(color => {
      stats.byColor[color] = 0;
    });
    DATA_LEVELS.forEach(level => {
      stats.byLevel[level] = 0;
    });

    // 统计数据
    Object.values(libraries).forEach(library => {
      stats.totalLibraries++;
      stats.totalWords += library.words.length;

      if (library.words.length === 0) {
        stats.emptyLibraries++;
      }

      const { color, level } = parseWordLibraryKey(library.key);
      stats.byColor[color] += library.words.length;
      stats.byLevel[level] += library.words.length;
    });

    stats.averageWordsPerLibrary = stats.totalLibraries > 0
      ? Math.round(stats.totalWords / stats.totalLibraries * 100) / 100
      : 0;

    return stats;
  }
}

// ===== 词库核心类 =====

export class WordLibraryCore {
  private dataManager = new WordLibraryDataManager();
  private validationEngine = new WordValidationEngine();

  /** 初始化词库 */
  initialize(): Record<WordLibraryKey, WordLibrary> {
    return this.dataManager.initializeWordLibraries();
  }

  /** 验证词语 */
  validateWord(word: string): WordValidationResult {
    return this.validationEngine.validateSingleWord(word);
  }

  /** 批量验证词语 */
  validateWords(words: string[]): Record<string, WordValidationResult> {
    return this.validationEngine.validateBatchWords(words);
  }

  /** 验证重复词语 */
  validateDuplicates(words: string[]) {
    return this.validationEngine.validateDuplicates(words);
  }

  /** 添加词语 */
  addWord(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey,
    word: string
  ): Record<WordLibraryKey, WordLibrary> {
    return this.dataManager.addWordToLibrary(libraries, key, word);
  }

  /** 移除词语 */
  removeWord(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey,
    word: string
  ): Record<WordLibraryKey, WordLibrary> {
    return this.dataManager.removeWordFromLibrary(libraries, key, word);
  }

  /** 设置词库词语 */
  setWords(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey,
    words: string[]
  ): Record<WordLibraryKey, WordLibrary> {
    return this.dataManager.setLibraryWords(libraries, key, words);
  }

  /** 清空词库 */
  clearLibrary(
    libraries: Record<WordLibraryKey, WordLibrary>,
    key: WordLibraryKey
  ): Record<WordLibraryKey, WordLibrary> {
    return this.dataManager.clearLibrary(libraries, key);
  }

  /** 获取统计信息 */
  getStats(libraries: Record<WordLibraryKey, WordLibrary>) {
    return this.dataManager.getLibraryStats(libraries);
  }

  /** 清理资源 */
  cleanup(): void {
    this.validationEngine.clearValidationCache();
  }
}

// ===== 词库状态管理 =====

interface WordLibraryState {
  /** 所有词库数据 */
  libraries: Record<WordLibraryKey, WordLibrary>;
  /** 当前选中的词库键 */
  selectedLibraryKey?: WordLibraryKey;
  /** 当前临时输入的词语 */
  temporaryWord: string;
  /** 是否处于编辑模式 */
  isEditMode: boolean;
  /** 是否处于加载状态 */
  isLoading: boolean;
  /** 错误信息 */
  error?: string;

  // 操作方法
  /** 初始化词库 */
  initialize: () => void;
  /** 选择词库 */
  selectLibrary: (key: WordLibraryKey) => void;
  /** 设置临时词语 */
  setTemporaryWord: (word: string) => void;
  /** 添加词语 */
  addWord: (key: WordLibraryKey, word: string) => void;
  /** 移除词语 */
  removeWord: (key: WordLibraryKey, word: string) => void;
  /** 批量设置词语 */
  setWords: (key: WordLibraryKey, words: string[]) => void;
  /** 清空词库 */
  clearLibrary: (key: WordLibraryKey) => void;
  /** 切换编辑模式 */
  toggleEditMode: () => void;
  /** 清除选择 */
  clearSelection: () => void;
  /** 重置所有词库 */
  reset: () => void;
}

export const useWordLibraryStore = create<WordLibraryState>()(
  persist(
    (set, get) => ({
      // 初始状态
      libraries: wordLibraryCore.initialize(),
      selectedLibraryKey: undefined,
      temporaryWord: '',
      isEditMode: false,
      isLoading: false,
      error: undefined,

      // 操作方法
      initialize: () => {
        set({
          libraries: wordLibraryCore.initialize(),
          isLoading: false,
          error: undefined,
        });
      },

      selectLibrary: (key) => {
        set({ selectedLibraryKey: key });
      },

      setTemporaryWord: (word) => {
        set({ temporaryWord: word });
      },

      addWord: (key, word) => {
        const state = get();
        const validation = wordLibraryCore.validateWord(word);

        if (!validation.isValid) {
          set({ error: validation.errors.join(', ') });
          return;
        }

        const updatedLibraries = wordLibraryCore.addWord(state.libraries, key, word);
        set({
          libraries: updatedLibraries,
          temporaryWord: '',
          error: undefined,
        });
      },

      removeWord: (key, word) => {
        const state = get();
        const updatedLibraries = wordLibraryCore.removeWord(state.libraries, key, word);
        set({ libraries: updatedLibraries });
      },

      setWords: (key, words) => {
        const state = get();
        const updatedLibraries = wordLibraryCore.setWords(state.libraries, key, words);
        set({ libraries: updatedLibraries });
      },

      clearLibrary: (key) => {
        const state = get();
        const updatedLibraries = wordLibraryCore.clearLibrary(state.libraries, key);
        set({ libraries: updatedLibraries });
      },

      toggleEditMode: () => {
        set(state => ({ isEditMode: !state.isEditMode }));
      },

      clearSelection: () => {
        set({
          selectedLibraryKey: undefined,
          temporaryWord: '',
          isEditMode: false,
        });
      },

      reset: () => {
        set({
          libraries: wordLibraryCore.initialize(),
          selectedLibraryKey: undefined,
          temporaryWord: '',
          isEditMode: false,
          isLoading: false,
          error: undefined,
        });
      },
    }),
    {
      name: STORAGE_KEYS.WORD_LIBRARIES,
      partialize: (state) => ({
        libraries: state.libraries,
      }),
    }
  )
);

// ===== 便捷选择器 =====

/** 获取指定词库 */
export const useWordLibrary = (key: WordLibraryKey) => {
  return useWordLibraryStore(state => state.libraries[key]);
};

/** 获取当前选中的词库 */
export const useSelectedWordLibrary = () => {
  return useWordLibraryStore(state => {
    if (!state.selectedLibraryKey) return null;
    return state.libraries[state.selectedLibraryKey];
  });
};

/** 获取词库统计信息 */
export const useWordLibraryStats = () => {
  return useWordLibraryStore(state => {
    return wordLibraryCore.getStats(state.libraries);
  });
};

/** 获取所有非空词库 */
export const useNonEmptyLibraries = () => {
  return useWordLibraryStore(state => {
    const nonEmpty: Record<WordLibraryKey, WordLibrary> = {};
    Object.entries(state.libraries).forEach(([key, library]) => {
      if (library.words.length > 0) {
        nonEmpty[key as WordLibraryKey] = library;
      }
    });
    return nonEmpty;
  });
};

// ===== 单例实例 =====

export const wordLibraryCore = new WordLibraryCore();
