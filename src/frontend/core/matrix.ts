/**
 * 矩阵核心模块 - 整合版本
 * 🎯 核心价值：统一的矩阵处理引擎，整合所有矩阵相关逻辑
 * 📦 功能范围：数据管理、事件处理、样式计算、渲染优化
 * 🔄 架构设计：单一职责的核心类，集成所有矩阵功能
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  DEBOUNCE_DELAYS,
  DEFAULT_MATRIX_CONFIG,
  MATRIX_SIZE,
  STORAGE_KEYS
} from '../../shared/constants';
import type {
  CellData,
  CellRenderData,
  Coordinate,
  EventHandlerOptions,
  MatrixConfig,
  MatrixData,
  StyleCalculationOptions
} from '../../shared/types';
import {
  coordinateKey,
  debounce,
  getColorClassName,
  getColorHex,
  isValidCoordinate,
  throttle
} from '../../shared/utils';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== 矩阵数据管理 =====

class MatrixDataManager {
  private cache = new Map<string, any>();

  /** 创建默认单元格 */
  createDefaultCell(coordinate: Coordinate): CellData {
    return {
      coordinate,
      color: 'white',
      level: 1,
      isActive: false,
    };
  }

  /** 初始化矩阵数据 */
  initializeMatrixData(): MatrixData {
    const cells: CellData[] = [];

    for (let x = 0; x < MATRIX_SIZE; x++) {
      for (let y = 0; y < MATRIX_SIZE; y++) {
        cells.push(this.createDefaultCell({ x, y }));
      }
    }

    return {
      cells,
      size: MATRIX_SIZE,
      version: '1.0.0',
      lastUpdated: new Date(),
    };
  }

  /** 获取单元格数据 */
  getCellData(matrixData: MatrixData, coordinate: Coordinate): CellData | undefined {
    const key = coordinateKey(coordinate);

    // 检查缓存
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    const cell = matrixData.cells.find(
      cell => cell.coordinate.x === coordinate.x && cell.coordinate.y === coordinate.y
    );

    // 缓存结果
    if (cell) {
      this.cache.set(key, cell);
    }

    return cell;
  }

  /** 更新单元格数据 */
  updateCellData(matrixData: MatrixData, coordinate: Coordinate, updates: Partial<CellData>): MatrixData {
    const key = coordinateKey(coordinate);

    return produce(matrixData, draft => {
      const cellIndex = draft.cells.findIndex(
        cell => cell.coordinate.x === coordinate.x && cell.coordinate.y === coordinate.y
      );

      if (cellIndex !== -1) {
        Object.assign(draft.cells[cellIndex], updates);
        draft.lastUpdated = new Date();

        // 更新缓存
        this.cache.set(key, draft.cells[cellIndex]);
      }
    });
  }

  /** 批量更新单元格 */
  batchUpdateCells(
    matrixData: MatrixData,
    updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>
  ): MatrixData {
    return produce(matrixData, draft => {
      updates.forEach(({ coordinate, data }) => {
        const cellIndex = draft.cells.findIndex(
          cell => cell.coordinate.x === coordinate.x && cell.coordinate.y === coordinate.y
        );

        if (cellIndex !== -1) {
          Object.assign(draft.cells[cellIndex], data);

          // 更新缓存
          const key = coordinateKey(coordinate);
          this.cache.set(key, draft.cells[cellIndex]);
        }
      });

      draft.lastUpdated = new Date();
    });
  }

  /** 清除缓存 */
  clearCache(): void {
    this.cache.clear();
  }
}

// ===== 样式计算引擎 =====

class StyleCalculationEngine {
  private styleCache = new Map<string, { className: string; style: React.CSSProperties }>();

  /** 计算单元格样式 */
  calculateCellStyle(options: StyleCalculationOptions): { className: string; style: React.CSSProperties } {
    const cacheKey = this.generateCacheKey(options);

    // 检查缓存
    if (this.styleCache.has(cacheKey)) {
      return this.styleCache.get(cacheKey)!;
    }

    const { coordinate, cellData, config, isEnhanced, customStyle, customClassName } = options;

    // 基础类名
    let className = 'matrix-cell';

    // 添加模式类名
    if (config?.mode) {
      className += ` matrix-cell--${config.mode}`;
    }

    // 添加颜色类名
    if (cellData?.color && config?.isColorMode) {
      className += ` ${getColorClassName(cellData.color)}`;
    }

    // 添加状态类名
    if (cellData?.isActive) {
      className += ' matrix-cell--active';
    }

    if (isEnhanced) {
      className += ' matrix-cell--enhanced';
    }

    // 添加自定义类名
    if (customClassName) {
      className += ` ${customClassName}`;
    }

    // 计算样式
    const style: React.CSSProperties = {
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '12px',
      fontWeight: 'normal',
      cursor: 'pointer',
      userSelect: 'none',
      ...customStyle,
    };

    // 添加颜色样式
    if (cellData?.color && config?.isColorMode) {
      style.backgroundColor = getColorHex(cellData.color);
      style.color = cellData.color === 'black' ? '#ffffff' : '#000000';
    }

    // 添加增强样式
    if (isEnhanced) {
      style.transform = 'scale(1.1)';
      style.zIndex = 10;
      style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
    }

    const result = { className, style };

    // 缓存结果
    this.styleCache.set(cacheKey, result);

    return result;
  }

  /** 生成缓存键 */
  private generateCacheKey(options: StyleCalculationOptions): string {
    const { coordinate, cellData, config, isEnhanced, customClassName } = options;
    return [
      coordinateKey(coordinate),
      cellData?.color || 'white',
      cellData?.level || 1,
      cellData?.isActive || false,
      config?.mode || 'color-word',
      isEnhanced || false,
      customClassName || '',
    ].join('|');
  }

  /** 清除样式缓存 */
  clearStyleCache(): void {
    this.styleCache.clear();
  }
}

// ===== 事件处理引擎 =====

class EventHandlingEngine {
  private eventHandlers = new Map<string, Function>();
  private debouncedHandlers = new Map<string, Function>();
  private throttledHandlers = new Map<string, Function>();

  /** 创建坐标事件处理器 */
  createCoordinateHandler(
    handler: (coordinate: Coordinate, event: Event) => void,
    options: EventHandlerOptions = {}
  ): (event: Event) => void {
    const {
      enableDebounce = false,
      debounceDelay = DEBOUNCE_DELAYS.USER_INPUT,
      enableThrottle = false,
      throttleDelay = 100,
      preventDefault = false,
      stopPropagation = false,
    } = options;

    let processedHandler = handler;

    // 应用防抖
    if (enableDebounce) {
      const key = `debounce_${Date.now()}`;
      const debouncedHandler = debounce(handler, debounceDelay);
      this.debouncedHandlers.set(key, debouncedHandler);
      processedHandler = debouncedHandler;
    }

    // 应用节流
    if (enableThrottle) {
      const key = `throttle_${Date.now()}`;
      const throttledHandler = throttle(handler, throttleDelay);
      this.throttledHandlers.set(key, throttledHandler);
      processedHandler = throttledHandler;
    }

    return (event: Event) => {
      if (preventDefault) {
        event.preventDefault();
      }
      if (stopPropagation) {
        event.stopPropagation();
      }

      // 从事件目标提取坐标
      const target = event.target as HTMLElement;
      const coordinateData = target.dataset.coordinate;

      if (coordinateData) {
        const [x, y] = coordinateData.split(',').map(Number);
        const coordinate: Coordinate = { x, y };

        if (isValidCoordinate(coordinate)) {
          processedHandler(coordinate, event);
        }
      }
    };
  }

  /** 创建键盘导航处理器 */
  createKeyboardHandler(
    onNavigate: (direction: 'up' | 'down' | 'left' | 'right') => void,
    onSelect: () => void,
    onEscape: () => void
  ): (event: KeyboardEvent) => void {
    return (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault();
          onNavigate('up');
          break;
        case 'ArrowDown':
          event.preventDefault();
          onNavigate('down');
          break;
        case 'ArrowLeft':
          event.preventDefault();
          onNavigate('left');
          break;
        case 'ArrowRight':
          event.preventDefault();
          onNavigate('right');
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          onSelect();
          break;
        case 'Escape':
          event.preventDefault();
          onEscape();
          break;
      }
    };
  }

  /** 清理事件处理器 */
  cleanup(): void {
    this.eventHandlers.clear();
    this.debouncedHandlers.clear();
    this.throttledHandlers.clear();
  }
}

// ===== 渲染引擎 =====

class RenderingEngine {
  /** 生成单元格渲染数据 */
  generateCellRenderData(
    cellData: CellData,
    config: MatrixConfig
  ): CellRenderData {
    let content = '';

    // 根据模式生成内容
    switch (config.mode) {
      case 'coordinate':
        content = `${cellData.coordinate.x},${cellData.coordinate.y}`;
        break;
      case 'color':
        content = cellData.color;
        break;
      case 'level':
        content = cellData.level.toString();
        break;
      case 'word':
        content = cellData.word || '';
        break;
      case 'color-word':
        content = cellData.word || cellData.color;
        break;
      case 'number-word':
        content = cellData.word || cellData.value?.toString() || '';
        break;
      default:
        content = '';
    }

    return {
      content,
      className: `matrix-cell matrix-cell--${config.mode}`,
      style: {},
      isInteractive: true,
    };
  }

  /** 批量生成渲染数据 */
  batchGenerateRenderData(
    cells: CellData[],
    config: MatrixConfig
  ): CellRenderData[] {
    return cells.map(cell => this.generateCellRenderData(cell, config));
  }
}

// ===== 矩阵核心类 =====

export class MatrixCore {
  private dataManager = new MatrixDataManager();
  private styleEngine = new StyleCalculationEngine();
  private eventEngine = new EventHandlingEngine();
  private renderEngine = new RenderingEngine();

  /** 初始化矩阵 */
  initialize(): MatrixData {
    return this.dataManager.initializeMatrixData();
  }

  /** 获取单元格数据 */
  getCellData(matrixData: MatrixData, coordinate: Coordinate): CellData | undefined {
    return this.dataManager.getCellData(matrixData, coordinate);
  }

  /** 更新单元格 */
  updateCell(matrixData: MatrixData, coordinate: Coordinate, updates: Partial<CellData>): MatrixData {
    return this.dataManager.updateCellData(matrixData, coordinate, updates);
  }

  /** 批量更新单元格 */
  batchUpdateCells(
    matrixData: MatrixData,
    updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>
  ): MatrixData {
    return this.dataManager.batchUpdateCells(matrixData, updates);
  }

  /** 计算单元格样式 */
  calculateCellStyle(options: StyleCalculationOptions) {
    return this.styleEngine.calculateCellStyle(options);
  }

  /** 创建事件处理器 */
  createEventHandler(
    handler: (coordinate: Coordinate, event: Event) => void,
    options?: EventHandlerOptions
  ) {
    return this.eventEngine.createCoordinateHandler(handler, options);
  }

  /** 创建键盘处理器 */
  createKeyboardHandler(
    onNavigate: (direction: 'up' | 'down' | 'left' | 'right') => void,
    onSelect: () => void,
    onEscape: () => void
  ) {
    return this.eventEngine.createKeyboardHandler(onNavigate, onSelect, onEscape);
  }

  /** 生成渲染数据 */
  generateRenderData(cellData: CellData, config: MatrixConfig): CellRenderData {
    return this.renderEngine.generateCellRenderData(cellData, config);
  }

  /** 清理资源 */
  cleanup(): void {
    this.dataManager.clearCache();
    this.styleEngine.clearStyleCache();
    this.eventEngine.cleanup();
  }
}

// ===== 矩阵状态管理 =====

interface MatrixState {
  /** 矩阵数据 */
  matrixData: MatrixData;
  /** 矩阵配置 */
  config: MatrixConfig;
  /** 当前选中的坐标 */
  selectedCoordinate?: Coordinate;
  /** 是否处于加载状态 */
  isLoading: boolean;
  /** 错误信息 */
  error?: string;

  // 操作方法
  /** 初始化矩阵 */
  initialize: () => void;
  /** 更新配置 */
  updateConfig: (config: Partial<MatrixConfig>) => void;
  /** 选择单元格 */
  selectCell: (coordinate: Coordinate) => void;
  /** 更新单元格 */
  updateCell: (coordinate: Coordinate, updates: Partial<CellData>) => void;
  /** 批量更新单元格 */
  batchUpdateCells: (updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>) => void;
  /** 清除选择 */
  clearSelection: () => void;
  /** 重置矩阵 */
  reset: () => void;
}

export const useMatrixStore = create<MatrixState>()(
  persist(
    (set, get) => ({
      // 初始状态
      matrixData: matrixCore.initialize(),
      config: DEFAULT_MATRIX_CONFIG,
      selectedCoordinate: undefined,
      isLoading: false,
      error: undefined,

      // 操作方法
      initialize: () => {
        set({
          matrixData: matrixCore.initialize(),
          isLoading: false,
          error: undefined
        });
      },

      updateConfig: (newConfig) => {
        set(state => ({
          config: { ...state.config, ...newConfig }
        }));
      },

      selectCell: (coordinate) => {
        if (isValidCoordinate(coordinate)) {
          set({ selectedCoordinate: coordinate });
        }
      },

      updateCell: (coordinate, updates) => {
        const state = get();
        const updatedMatrixData = matrixCore.updateCell(state.matrixData, coordinate, updates);
        set({ matrixData: updatedMatrixData });
      },

      batchUpdateCells: (updates) => {
        const state = get();
        const updatedMatrixData = matrixCore.batchUpdateCells(state.matrixData, updates);
        set({ matrixData: updatedMatrixData });
      },

      clearSelection: () => {
        set({ selectedCoordinate: undefined });
      },

      reset: () => {
        set({
          matrixData: matrixCore.initialize(),
          config: DEFAULT_MATRIX_CONFIG,
          selectedCoordinate: undefined,
          isLoading: false,
          error: undefined,
        });
      },
    }),
    {
      name: STORAGE_KEYS.MATRIX_DATA,
      partialize: (state) => ({
        matrixData: state.matrixData,
        config: state.config,
      }),
    }
  )
);

// ===== 便捷选择器 =====

/** 获取单元格数据 */
export const useCellData = (coordinate: Coordinate) => {
  return useMatrixStore(state => {
    return matrixCore.getCellData(state.matrixData, coordinate);
  });
};

/** 获取选中状态 */
export const useIsSelected = (coordinate: Coordinate) => {
  return useMatrixStore(state => {
    const selected = state.selectedCoordinate;
    return selected ? selected.x === coordinate.x && selected.y === coordinate.y : false;
  });
};

/** 获取渲染数据 */
export const useCellRenderData = (coordinate: Coordinate) => {
  return useMatrixStore(state => {
    const cellData = matrixCore.getCellData(state.matrixData, coordinate);
    if (!cellData) return null;
    return matrixCore.generateRenderData(cellData, state.config);
  });
};

// ===== 单例实例 =====

export const matrixCore = new MatrixCore();
