/**
 * 矩阵网格组件 - 整合版本
 * 🎯 核心价值：优化的矩阵网格渲染组件，支持虚拟化和性能优化
 * 📦 功能范围：网格布局、虚拟化渲染、性能监控
 * 🔄 架构设计：基于性能优化的网格渲染组件
 */

'use client';

import React, { memo, useMemo, useRef, useEffect, useState } from 'react';
import { MATRIX_SIZE } from '../../../shared/constants';
import { useMatrixData } from '../../hooks/useMatrix';
import { MatrixCell } from './MatrixCell';

// ===== 组件属性 =====

interface MatrixGridProps {
  /** 自定义样式 */
  className?: string;
  style?: React.CSSProperties;
  
  /** 网格配置 */
  showGridLines?: boolean;
  cellSize?: number;
  gap?: number;
  
  /** 性能配置 */
  enableVirtualization?: boolean;
  viewportWidth?: number;
  viewportHeight?: number;
  overscan?: number;
  
  /** 事件回调 */
  onCellClick?: (coordinate: { x: number; y: number }) => void;
  onCellDoubleClick?: (coordinate: { x: number; y: number }) => void;
  
  /** 渲染配置 */
  renderBatchSize?: number;
  enableProgressiveRendering?: boolean;
}

// ===== 虚拟化Hook =====

const useVirtualization = (
  enabled: boolean,
  viewportWidth: number,
  viewportHeight: number,
  cellSize: number,
  gap: number,
  overscan: number = 2
) => {
  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });
  
  return useMemo(() => {
    if (!enabled) {
      // 返回所有单元格
      const allCells = [];
      for (let y = 0; y < MATRIX_SIZE; y++) {
        for (let x = 0; x < MATRIX_SIZE; x++) {
          allCells.push({ x, y });
        }
      }
      return { visibleCells: allCells, totalWidth: MATRIX_SIZE * (cellSize + gap), totalHeight: MATRIX_SIZE * (cellSize + gap) };
    }

    const cellWithGap = cellSize + gap;
    
    // 计算可见范围
    const startX = Math.max(0, Math.floor(scrollPosition.x / cellWithGap) - overscan);
    const endX = Math.min(MATRIX_SIZE - 1, Math.ceil((scrollPosition.x + viewportWidth) / cellWithGap) + overscan);
    const startY = Math.max(0, Math.floor(scrollPosition.y / cellWithGap) - overscan);
    const endY = Math.min(MATRIX_SIZE - 1, Math.ceil((scrollPosition.y + viewportHeight) / cellWithGap) + overscan);
    
    // 生成可见单元格列表
    const visibleCells = [];
    for (let y = startY; y <= endY; y++) {
      for (let x = startX; x <= endX; x++) {
        visibleCells.push({ x, y });
      }
    }
    
    return {
      visibleCells,
      totalWidth: MATRIX_SIZE * cellWithGap,
      totalHeight: MATRIX_SIZE * cellWithGap,
      startX,
      startY,
      endX,
      endY,
    };
  }, [enabled, scrollPosition, viewportWidth, viewportHeight, cellSize, gap, overscan]);
};

// ===== 渐进式渲染Hook =====

const useProgressiveRendering = (
  enabled: boolean,
  totalCells: number,
  batchSize: number = 100
) => {
  const [renderedCount, setRenderedCount] = useState(enabled ? batchSize : totalCells);
  
  useEffect(() => {
    if (!enabled || renderedCount >= totalCells) return;
    
    const timer = setTimeout(() => {
      setRenderedCount(prev => Math.min(prev + batchSize, totalCells));
    }, 16); // 约60fps
    
    return () => clearTimeout(timer);
  }, [enabled, renderedCount, totalCells, batchSize]);
  
  return renderedCount;
};

// ===== 主组件 =====

export const MatrixGrid = memo<MatrixGridProps>(({
  className = '',
  style,
  showGridLines = true,
  cellSize = 33,
  gap = 1,
  enableVirtualization = false,
  viewportWidth = 800,
  viewportHeight = 600,
  overscan = 2,
  onCellClick,
  onCellDoubleClick,
  renderBatchSize = 100,
  enableProgressiveRendering = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { matrixData } = useMatrixData();
  
  // 虚拟化计算
  const virtualization = useVirtualization(
    enableVirtualization,
    viewportWidth,
    viewportHeight,
    cellSize,
    gap,
    overscan
  );
  
  // 渐进式渲染
  const renderedCount = useProgressiveRendering(
    enableProgressiveRendering,
    virtualization.visibleCells.length,
    renderBatchSize
  );
  
  // 计算要渲染的单元格
  const cellsToRender = useMemo(() => {
    const cells = enableProgressiveRendering 
      ? virtualization.visibleCells.slice(0, renderedCount)
      : virtualization.visibleCells;
    
    return cells;
  }, [virtualization.visibleCells, enableProgressiveRendering, renderedCount]);
  
  // 计算网格样式
  const gridStyle: React.CSSProperties = useMemo(() => ({
    display: 'grid',
    gridTemplateColumns: `repeat(${MATRIX_SIZE}, ${cellSize}px)`,
    gridTemplateRows: `repeat(${MATRIX_SIZE}, ${cellSize}px)`,
    gap: showGridLines ? `${gap}px` : '0',
    backgroundColor: showGridLines ? '#e5e7eb' : 'transparent',
    position: 'relative',
    width: enableVirtualization ? virtualization.totalWidth : 'auto',
    height: enableVirtualization ? virtualization.totalHeight : 'auto',
    ...style,
  }), [cellSize, gap, showGridLines, enableVirtualization, virtualization, style]);
  
  // 单元格点击处理
  const handleCellClick = (coordinate: { x: number; y: number }) => {
    onCellClick?.(coordinate);
  };
  
  const handleCellDoubleClick = (coordinate: { x: number; y: number }) => {
    onCellDoubleClick?.(coordinate);
  };
  
  // 滚动处理（用于虚拟化）
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    if (!enableVirtualization) return;
    
    const target = event.target as HTMLDivElement;
    // 这里可以更新滚动位置，但需要状态管理
    // setScrollPosition({ x: target.scrollLeft, y: target.scrollTop });
  };
  
  return (
    <div
      ref={containerRef}
      className={`matrix-grid ${className}`}
      style={gridStyle}
      onScroll={handleScroll}
      data-testid="matrix-grid"
      role="grid"
      aria-label="矩阵网格"
    >
      {cellsToRender.map(({ x, y }) => {
        const coordinate = { x, y };
        
        // 虚拟化时需要绝对定位
        const cellStyle = enableVirtualization ? {
          position: 'absolute' as const,
          left: x * (cellSize + gap),
          top: y * (cellSize + gap),
          width: cellSize,
          height: cellSize,
        } : undefined;
        
        return (
          <MatrixCell
            key={`${x}-${y}`}
            coordinate={coordinate}
            onClick={() => handleCellClick(coordinate)}
            onDoubleClick={handleCellDoubleClick}
            style={cellStyle}
          />
        );
      })}
      
      {/* 渐进式渲染进度指示器 */}
      {enableProgressiveRendering && renderedCount < virtualization.visibleCells.length && (
        <div
          className="matrix-grid__loading-indicator"
          style={{
            position: 'absolute',
            bottom: '10px',
            right: '10px',
            padding: '4px 8px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            borderRadius: '4px',
            fontSize: '12px',
            pointerEvents: 'none',
            zIndex: 100,
          }}
        >
          渲染中... {Math.round((renderedCount / virtualization.visibleCells.length) * 100)}%
        </div>
      )}
      
      {/* 虚拟化调试信息 */}
      {process.env.NODE_ENV === 'development' && enableVirtualization && (
        <div
          className="matrix-grid__debug-info"
          style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            padding: '4px 8px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            borderRadius: '4px',
            fontSize: '10px',
            fontFamily: 'monospace',
            pointerEvents: 'none',
            zIndex: 100,
          }}
        >
          <div>可见单元格: {virtualization.visibleCells.length}</div>
          <div>已渲染: {renderedCount}</div>
          <div>总计: {MATRIX_SIZE * MATRIX_SIZE}</div>
        </div>
      )}
    </div>
  );
});

MatrixGrid.displayName = 'MatrixGrid';

// ===== 网格布局工具组件 =====

/**
 * 网格容器组件
 */
export const MatrixGridContainer = memo<{
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  enableScrolling?: boolean;
}>(({ children, className, style, enableScrolling = true }) => (
  <div
    className={`matrix-grid-container ${className || ''}`}
    style={{
      position: 'relative',
      overflow: enableScrolling ? 'auto' : 'hidden',
      ...style,
    }}
  >
    {children}
  </div>
));

MatrixGridContainer.displayName = 'MatrixGridContainer';

/**
 * 网格覆盖层组件
 */
export const MatrixGridOverlay = memo<{
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}>(({ children, className, style }) => (
  <div
    className={`matrix-grid-overlay ${className || ''}`}
    style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      pointerEvents: 'none',
      zIndex: 50,
      ...style,
    }}
  >
    {children}
  </div>
));

MatrixGridOverlay.displayName = 'MatrixGridOverlay';

export default MatrixGrid;
