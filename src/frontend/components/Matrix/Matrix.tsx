/**
 * 矩阵主组件 - 整合版本
 * 🎯 核心价值：统一的矩阵渲染和交互组件，整合所有矩阵功能
 * 📦 功能范围：矩阵渲染、事件处理、键盘导航、性能优化
 * 🔄 架构设计：基于整合后的Hook和核心模块的React组件
 */

'use client';

import React, { memo, useCallback, useEffect, useRef } from 'react';
import { MATRIX_SIZE } from '../../../shared/constants';
import { useMatrix, useMatrixEvents, useMatrixNavigation } from '../../hooks/useMatrix';
import { MatrixCell } from './MatrixCell';
import { MatrixGrid } from './MatrixGrid';

// ===== 组件属性 =====

interface MatrixProps {
  /** 自定义样式 */
  className?: string;
  style?: React.CSSProperties;
  
  /** 事件回调 */
  onCellClick?: (coordinate: { x: number; y: number }) => void;
  onCellDoubleClick?: (coordinate: { x: number; y: number }) => void;
  onSelectionChange?: (coordinate?: { x: number; y: number }) => void;
  
  /** 显示配置 */
  showGridLines?: boolean;
  showCoordinates?: boolean;
  enableKeyboardNavigation?: boolean;
  
  /** 性能配置 */
  enableVirtualization?: boolean;
  renderBatchSize?: number;
}

// ===== 主组件 =====

export const Matrix = memo<MatrixProps>(({
  className = '',
  style,
  onCellClick,
  onCellDoubleClick,
  onSelectionChange,
  showGridLines = true,
  showCoordinates = false,
  enableKeyboardNavigation = true,
  enableVirtualization = false,
  renderBatchSize = 100,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 使用整合后的Hook
  const {
    matrixData,
    config,
    selectedCoordinate,
    isLoading,
    error,
  } = useMatrix();
  
  const { createClickHandler, createKeyboardHandler } = useMatrixEvents({
    enableDebounce: true,
    debounceDelay: 100,
  });
  
  const { moveTo } = useMatrixNavigation();

  // 单元格点击处理
  const handleCellClick = useCallback(createClickHandler((coordinate, cellData) => {
    onCellClick?.(coordinate);
    onSelectionChange?.(coordinate);
  }), [createClickHandler, onCellClick, onSelectionChange]);

  // 单元格双击处理
  const handleCellDoubleClick = useCallback((coordinate: { x: number; y: number }) => {
    onCellDoubleClick?.(coordinate);
  }, [onCellDoubleClick]);

  // 键盘导航处理
  const keyboardHandler = useCallback(createKeyboardHandler(selectedCoordinate), [
    createKeyboardHandler,
    selectedCoordinate,
  ]);

  // 绑定键盘事件
  useEffect(() => {
    if (!enableKeyboardNavigation) return;

    const container = containerRef.current;
    if (!container) return;

    // 确保容器可以获得焦点
    container.tabIndex = 0;
    container.addEventListener('keydown', keyboardHandler);

    return () => {
      container.removeEventListener('keydown', keyboardHandler);
    };
  }, [enableKeyboardNavigation, keyboardHandler]);

  // 选择变化通知
  useEffect(() => {
    onSelectionChange?.(selectedCoordinate);
  }, [selectedCoordinate, onSelectionChange]);

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className={`matrix-container matrix-container--loading ${className}`} style={style}>
        <div className="matrix-loading">
          <div className="matrix-loading__spinner" />
          <div className="matrix-loading__text">加载矩阵数据...</div>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className={`matrix-container matrix-container--error ${className}`} style={style}>
        <div className="matrix-error">
          <div className="matrix-error__icon">⚠️</div>
          <div className="matrix-error__message">{error}</div>
          <button 
            className="matrix-error__retry"
            onClick={() => window.location.reload()}
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  // 计算容器样式
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: 'fit-content',
    height: 'fit-content',
    outline: 'none',
    ...style,
  };

  // 计算网格样式
  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${MATRIX_SIZE}, 1fr)`,
    gridTemplateRows: `repeat(${MATRIX_SIZE}, 1fr)`,
    gap: showGridLines ? '1px' : '0',
    backgroundColor: showGridLines ? '#e5e7eb' : 'transparent',
    padding: '6px',
  };

  return (
    <div
      ref={containerRef}
      className={`matrix-container ${className}`}
      style={containerStyle}
      data-testid="matrix-container"
    >
      {/* 矩阵网格 */}
      <div
        className="matrix-grid"
        style={gridStyle}
        data-testid="matrix-grid"
      >
        {matrixData.cells.map((cellData) => (
          <MatrixCell
            key={`${cellData.coordinate.x}-${cellData.coordinate.y}`}
            coordinate={cellData.coordinate}
            onClick={handleCellClick}
            onDoubleClick={handleCellDoubleClick}
            showCoordinates={showCoordinates}
            data-coordinate={`${cellData.coordinate.x},${cellData.coordinate.y}`}
          />
        ))}
      </div>

      {/* 选中指示器 */}
      {selectedCoordinate && (
        <div
          className="matrix-selection-indicator"
          style={{
            position: 'absolute',
            left: `${(selectedCoordinate.x * 34) + 6}px`,
            top: `${(selectedCoordinate.y * 34) + 6}px`,
            width: '33px',
            height: '33px',
            border: '2px solid #3b82f6',
            borderRadius: '4px',
            pointerEvents: 'none',
            zIndex: 10,
          }}
          data-testid="selection-indicator"
        />
      )}

      {/* 坐标显示 */}
      {showCoordinates && selectedCoordinate && (
        <div
          className="matrix-coordinate-display"
          style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            padding: '4px 8px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: 'monospace',
            pointerEvents: 'none',
            zIndex: 20,
          }}
          data-testid="coordinate-display"
        >
          ({selectedCoordinate.x}, {selectedCoordinate.y})
        </div>
      )}

      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div
          className="matrix-debug-info"
          style={{
            position: 'absolute',
            bottom: '10px',
            left: '10px',
            padding: '4px 8px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            borderRadius: '4px',
            fontSize: '10px',
            fontFamily: 'monospace',
            pointerEvents: 'none',
            zIndex: 20,
          }}
        >
          <div>模式: {config.mode}</div>
          <div>单元格: {matrixData.cells.length}</div>
          <div>版本: {matrixData.version}</div>
        </div>
      )}
    </div>
  );
});

Matrix.displayName = 'Matrix';

// ===== 样式常量 =====

export const MATRIX_STYLES = {
  container: {
    base: 'matrix-container',
    loading: 'matrix-container--loading',
    error: 'matrix-container--error',
  },
  grid: {
    base: 'matrix-grid',
    withLines: 'matrix-grid--with-lines',
    compact: 'matrix-grid--compact',
  },
  cell: {
    base: 'matrix-cell',
    selected: 'matrix-cell--selected',
    active: 'matrix-cell--active',
    enhanced: 'matrix-cell--enhanced',
  },
} as const;

export default Matrix;
