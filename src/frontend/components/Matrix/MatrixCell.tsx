/**
 * 矩阵单元格组件 - 整合版本
 * 🎯 核心价值：统一的单元格渲染和交互组件，整合所有单元格功能
 * 📦 功能范围：单元格渲染、样式计算、事件处理、状态管理
 * 🔄 架构设计：基于整合后的Hook和核心模块的React组件
 */

'use client';

import React, { memo, useCallback, useMemo } from 'react';
import type { Coordinate } from '../../../shared/types';
import { useMatrixCell, useMatrixCellStyle } from '../../hooks/useMatrix';

// ===== 组件属性 =====

interface MatrixCellProps {
  /** 单元格坐标 */
  coordinate: Coordinate;
  
  /** 事件回调 */
  onClick?: (event: React.MouseEvent) => void;
  onDoubleClick?: (coordinate: Coordinate) => void;
  onMouseEnter?: (coordinate: Coordinate) => void;
  onMouseLeave?: (coordinate: Coordinate) => void;
  
  /** 显示配置 */
  showCoordinates?: boolean;
  showTooltip?: boolean;
  
  /** 样式配置 */
  customClassName?: string;
  customStyle?: React.CSSProperties;
  
  /** 其他属性 */
  [key: string]: any;
}

// ===== 主组件 =====

export const MatrixCell = memo<MatrixCellProps>(({
  coordinate,
  onClick,
  onDoubleClick,
  onMouseEnter,
  onMouseLeave,
  showCoordinates = false,
  showTooltip = true,
  customClassName,
  customStyle,
  ...otherProps
}) => {
  // 使用整合后的Hook
  const {
    cellData,
    renderData,
    isSelected,
    select,
    toggleActive,
    setColor,
    setLevel,
    setWord,
  } = useMatrixCell(coordinate);

  const { className, style } = useMatrixCellStyle(coordinate, {
    customClassName,
    customStyle,
  });

  // 事件处理
  const handleClick = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    select();
    onClick?.(event);
  }, [select, onClick]);

  const handleDoubleClick = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    toggleActive();
    onDoubleClick?.(coordinate);
  }, [toggleActive, onDoubleClick, coordinate]);

  const handleMouseEnter = useCallback(() => {
    onMouseEnter?.(coordinate);
  }, [onMouseEnter, coordinate]);

  const handleMouseLeave = useCallback(() => {
    onMouseLeave?.(coordinate);
  }, [onMouseLeave, coordinate]);

  // 键盘事件处理
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        toggleActive();
        break;
      case 'Delete':
      case 'Backspace':
        event.preventDefault();
        if (cellData?.word) {
          setWord('');
        }
        break;
    }
  }, [toggleActive, cellData, setWord]);

  // 计算显示内容
  const displayContent = useMemo(() => {
    if (!renderData) return '';
    
    if (showCoordinates) {
      return `${coordinate.x},${coordinate.y}`;
    }
    
    return renderData.content;
  }, [renderData, showCoordinates, coordinate]);

  // 计算工具提示内容
  const tooltipContent = useMemo(() => {
    if (!showTooltip || !cellData) return undefined;
    
    const parts = [
      `坐标: (${coordinate.x}, ${coordinate.y})`,
      `颜色: ${cellData.color}`,
      `级别: ${cellData.level}`,
    ];
    
    if (cellData.word) {
      parts.push(`词语: ${cellData.word}`);
    }
    
    if (cellData.value !== undefined) {
      parts.push(`数值: ${cellData.value}`);
    }
    
    if (cellData.group) {
      parts.push(`分组: ${cellData.group}`);
    }
    
    parts.push(`状态: ${cellData.isActive ? '激活' : '未激活'}`);
    
    return parts.join('\n');
  }, [showTooltip, cellData, coordinate]);

  // 计算最终样式
  const finalStyle: React.CSSProperties = {
    ...style,
    // 确保单元格基础样式
    width: '33px',
    height: '33px',
    minWidth: '33px',
    minHeight: '33px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '10px',
    fontWeight: 'normal',
    cursor: 'pointer',
    userSelect: 'none',
    border: '1px solid transparent',
    borderRadius: '2px',
    transition: 'all 0.15s ease-in-out',
    
    // 选中状态样式
    ...(isSelected && {
      transform: 'scale(1.05)',
      zIndex: 10,
      boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
      borderColor: '#3b82f6',
    }),
    
    // 激活状态样式
    ...(cellData?.isActive && {
      fontWeight: 'bold',
      boxShadow: 'inset 0 0 0 2px rgba(34, 197, 94, 0.5)',
    }),
  };

  // 计算最终类名
  const finalClassName = [
    'matrix-cell',
    className,
    isSelected && 'matrix-cell--selected',
    cellData?.isActive && 'matrix-cell--active',
    cellData?.word && 'matrix-cell--has-word',
    customClassName,
  ].filter(Boolean).join(' ');

  return (
    <div
      className={finalClassName}
      style={finalStyle}
      title={tooltipContent}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onKeyDown={handleKeyDown}
      tabIndex={isSelected ? 0 : -1}
      role="gridcell"
      aria-selected={isSelected}
      aria-label={`单元格 ${coordinate.x}, ${coordinate.y}${cellData?.word ? `, 词语: ${cellData.word}` : ''}`}
      data-testid={`matrix-cell-${coordinate.x}-${coordinate.y}`}
      data-coordinate={`${coordinate.x},${coordinate.y}`}
      data-color={cellData?.color}
      data-level={cellData?.level}
      data-active={cellData?.isActive}
      data-selected={isSelected}
      {...otherProps}
    >
      {/* 主要内容 */}
      <span className="matrix-cell__content">
        {displayContent}
      </span>
      
      {/* 状态指示器 */}
      {cellData?.isActive && (
        <div
          className="matrix-cell__active-indicator"
          style={{
            position: 'absolute',
            top: '2px',
            right: '2px',
            width: '4px',
            height: '4px',
            backgroundColor: '#22c55e',
            borderRadius: '50%',
          }}
          aria-hidden="true"
        />
      )}
      
      {/* 词语指示器 */}
      {cellData?.word && (
        <div
          className="matrix-cell__word-indicator"
          style={{
            position: 'absolute',
            bottom: '2px',
            left: '2px',
            width: '4px',
            height: '4px',
            backgroundColor: '#8b5cf6',
            borderRadius: '50%',
          }}
          aria-hidden="true"
        />
      )}
      
      {/* 级别指示器 */}
      {cellData && cellData.level > 1 && (
        <div
          className="matrix-cell__level-indicator"
          style={{
            position: 'absolute',
            top: '2px',
            left: '2px',
            fontSize: '8px',
            fontWeight: 'bold',
            color: '#6b7280',
            lineHeight: 1,
          }}
          aria-hidden="true"
        >
          {cellData.level}
        </div>
      )}
    </div>
  );
});

MatrixCell.displayName = 'MatrixCell';

// ===== 辅助组件 =====

/**
 * 单元格内容组件
 */
export const MatrixCellContent = memo<{
  content: string;
  className?: string;
  style?: React.CSSProperties;
}>(({ content, className, style }) => (
  <span 
    className={`matrix-cell__content ${className || ''}`}
    style={style}
  >
    {content}
  </span>
));

MatrixCellContent.displayName = 'MatrixCellContent';

/**
 * 单元格指示器组件
 */
export const MatrixCellIndicator = memo<{
  type: 'active' | 'word' | 'level';
  value?: string | number;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  color?: string;
}>(({ type, value, position = 'top-right', color }) => {
  const positionStyles = {
    'top-left': { top: '2px', left: '2px' },
    'top-right': { top: '2px', right: '2px' },
    'bottom-left': { bottom: '2px', left: '2px' },
    'bottom-right': { bottom: '2px', right: '2px' },
  };

  const typeStyles = {
    active: { backgroundColor: '#22c55e', width: '4px', height: '4px', borderRadius: '50%' },
    word: { backgroundColor: '#8b5cf6', width: '4px', height: '4px', borderRadius: '50%' },
    level: { fontSize: '8px', fontWeight: 'bold', color: '#6b7280', lineHeight: 1 },
  };

  return (
    <div
      className={`matrix-cell__indicator matrix-cell__indicator--${type}`}
      style={{
        position: 'absolute',
        ...positionStyles[position],
        ...typeStyles[type],
        ...(color && { backgroundColor: color }),
      }}
      aria-hidden="true"
    >
      {type === 'level' && value}
    </div>
  );
});

MatrixCellIndicator.displayName = 'MatrixCellIndicator';

export default MatrixCell;
