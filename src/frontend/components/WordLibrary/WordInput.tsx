/**
 * 词语输入组件 - 整合版本
 * 🎯 核心价值：统一的词语输入和验证组件，整合输入处理功能
 * 📦 功能范围：词语输入、实时验证、键盘操作、错误提示
 * 🔄 架构设计：基于词语验证Hook的可复用输入组件
 */

'use client';

import React, { memo, useCallback, useEffect, useRef } from 'react';
import type { WordLibraryKey } from '../../../shared/types';
import { useWordLibraryInput } from '../../hooks/useWordLibrary';

// ===== 组件属性 =====

interface WordInputProps {
  /** 词库键 */
  libraryKey: WordLibraryKey;
  
  /** 输入配置 */
  placeholder?: string;
  maxLength?: number;
  autoFocus?: boolean;
  
  /** 事件回调 */
  onWordAdd?: (word: string) => void;
  onCancel?: () => void;
  onChange?: (value: string) => void;
  
  /** 样式配置 */
  className?: string;
  style?: React.CSSProperties;
  size?: 'small' | 'medium' | 'large';
  
  /** 显示配置 */
  showValidation?: boolean;
  showCounter?: boolean;
  showButtons?: boolean;
}

// ===== 主组件 =====

export const WordInput = memo<WordInputProps>(({
  libraryKey,
  placeholder = '输入词语...',
  maxLength = 10,
  autoFocus = false,
  onWordAdd,
  onCancel,
  onChange,
  className = '',
  style,
  size = 'medium',
  showValidation = true,
  showCounter = true,
  showButtons = true,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  
  // 使用词库输入Hook
  const {
    inputValue,
    validationResult,
    isValid,
    handleInputChange,
    handleKeyDown,
    submitWord,
    clearInput,
  } = useWordLibraryInput(libraryKey);

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // 输入变化处理
  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    handleInputChange(value);
    onChange?.(value);
  }, [handleInputChange, onChange]);

  // 提交处理
  const handleSubmit = useCallback(() => {
    const success = submitWord();
    if (success && onWordAdd) {
      onWordAdd(inputValue.trim());
    }
  }, [submitWord, onWordAdd, inputValue]);

  // 取消处理
  const handleCancel = useCallback(() => {
    clearInput();
    onCancel?.();
  }, [clearInput, onCancel]);

  // 键盘事件处理
  const handleKeyPress = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
    handleKeyDown(event);
    
    // 额外的键盘处理
    if (event.key === 'Escape') {
      handleCancel();
    }
  }, [handleKeyDown, handleCancel]);

  // 计算样式
  const sizeStyles = {
    small: {
      fontSize: '12px',
      padding: '4px 8px',
      height: '28px',
    },
    medium: {
      fontSize: '14px',
      padding: '6px 12px',
      height: '32px',
    },
    large: {
      fontSize: '16px',
      padding: '8px 16px',
      height: '40px',
    },
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
    ...style,
  };

  const inputContainerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  };

  const inputStyle: React.CSSProperties = {
    flex: 1,
    border: `1px solid ${isValid ? '#d1d5db' : '#ef4444'}`,
    borderRadius: '4px',
    outline: 'none',
    transition: 'border-color 0.2s ease-in-out',
    ...sizeStyles[size],
  };

  const buttonStyle: React.CSSProperties = {
    padding: '4px 12px',
    borderRadius: '4px',
    border: 'none',
    cursor: 'pointer',
    fontSize: '12px',
    fontWeight: '500',
    transition: 'all 0.2s ease-in-out',
  };

  const submitButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: isValid ? '#3b82f6' : '#9ca3af',
    color: 'white',
  };

  const cancelButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#f3f4f6',
    color: '#374151',
  };

  return (
    <div
      className={`word-input ${className}`}
      style={containerStyle}
      data-testid="word-input"
    >
      {/* 输入区域 */}
      <div style={inputContainerStyle}>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleChange}
          onKeyDown={handleKeyPress}
          placeholder={placeholder}
          maxLength={maxLength}
          style={inputStyle}
          className="word-input__field"
          aria-label="词语输入框"
          aria-invalid={!isValid}
          aria-describedby={
            showValidation && validationResult ? 'word-input-validation' : undefined
          }
        />
        
        {/* 字符计数器 */}
        {showCounter && (
          <span
            className="word-input__counter"
            style={{
              fontSize: '12px',
              color: inputValue.length > maxLength * 0.8 ? '#ef4444' : '#6b7280',
              minWidth: '40px',
              textAlign: 'right',
            }}
          >
            {inputValue.length}/{maxLength}
          </span>
        )}
        
        {/* 操作按钮 */}
        {showButtons && (
          <div className="word-input__buttons" style={{ display: 'flex', gap: '4px' }}>
            <button
              onClick={handleSubmit}
              disabled={!isValid || !inputValue.trim()}
              style={submitButtonStyle}
              className="word-input__submit"
              title="添加词语 (Enter)"
              aria-label="添加词语"
            >
              添加
            </button>
            
            {onCancel && (
              <button
                onClick={handleCancel}
                style={cancelButtonStyle}
                className="word-input__cancel"
                title="取消 (Escape)"
                aria-label="取消输入"
              >
                取消
              </button>
            )}
          </div>
        )}
      </div>

      {/* 验证信息 */}
      {showValidation && validationResult && (
        <div
          id="word-input-validation"
          className="word-input__validation"
          style={{
            fontSize: '12px',
            marginTop: '4px',
          }}
        >
          {/* 错误信息 */}
          {validationResult.errors.length > 0 && (
            <div
              className="validation-errors"
              style={{ color: '#ef4444' }}
            >
              {validationResult.errors.map((error, index) => (
                <div key={index} className="validation-error">
                  ⚠️ {error}
                </div>
              ))}
            </div>
          )}
          
          {/* 警告信息 */}
          {validationResult.warnings.length > 0 && (
            <div
              className="validation-warnings"
              style={{ color: '#f59e0b' }}
            >
              {validationResult.warnings.map((warning, index) => (
                <div key={index} className="validation-warning">
                  ⚡ {warning}
                </div>
              ))}
            </div>
          )}
          
          {/* 建议信息 */}
          {validationResult.suggestions.length > 0 && (
            <div
              className="validation-suggestions"
              style={{ color: '#3b82f6' }}
            >
              {validationResult.suggestions.map((suggestion, index) => (
                <div key={index} className="validation-suggestion">
                  💡 {suggestion}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 快捷键提示 */}
      <div
        className="word-input__shortcuts"
        style={{
          fontSize: '10px',
          color: '#9ca3af',
          marginTop: '2px',
        }}
      >
        Enter 添加 • Escape 取消
      </div>
    </div>
  );
});

WordInput.displayName = 'WordInput';

// ===== 简化版输入组件 =====

/**
 * 简化的词语输入组件
 */
export const SimpleWordInput = memo<{
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  placeholder?: string;
  className?: string;
}>(({ value, onChange, onSubmit, placeholder, className }) => (
  <input
    type="text"
    value={value}
    onChange={(e) => onChange(e.target.value)}
    onKeyDown={(e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        onSubmit();
      }
    }}
    placeholder={placeholder}
    className={`simple-word-input ${className || ''}`}
    style={{
      padding: '6px 12px',
      border: '1px solid #d1d5db',
      borderRadius: '4px',
      fontSize: '14px',
      outline: 'none',
    }}
  />
));

SimpleWordInput.displayName = 'SimpleWordInput';

// ===== 批量输入组件 =====

/**
 * 批量词语输入组件
 */
export const BatchWordInput = memo<{
  libraryKey: WordLibraryKey;
  onWordsAdd: (words: string[]) => void;
  onCancel?: () => void;
}>(({ libraryKey, onWordsAdd, onCancel }) => {
  const [textValue, setTextValue] = React.useState('');
  
  const handleSubmit = useCallback(() => {
    const words = textValue
      .split('\n')
      .map(word => word.trim())
      .filter(word => word.length > 0);
    
    if (words.length > 0) {
      onWordsAdd(words);
      setTextValue('');
    }
  }, [textValue, onWordsAdd]);

  return (
    <div className="batch-word-input">
      <textarea
        value={textValue}
        onChange={(e) => setTextValue(e.target.value)}
        placeholder="每行输入一个词语..."
        rows={5}
        style={{
          width: '100%',
          padding: '8px',
          border: '1px solid #d1d5db',
          borderRadius: '4px',
          fontSize: '14px',
          resize: 'vertical',
          outline: 'none',
        }}
      />
      <div style={{ display: 'flex', gap: '8px', marginTop: '8px' }}>
        <button
          onClick={handleSubmit}
          disabled={!textValue.trim()}
          style={{
            padding: '6px 16px',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          批量添加
        </button>
        {onCancel && (
          <button
            onClick={onCancel}
            style={{
              padding: '6px 16px',
              backgroundColor: '#f3f4f6',
              color: '#374151',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            取消
          </button>
        )}
      </div>
    </div>
  );
});

BatchWordInput.displayName = 'BatchWordInput';

export default WordInput;
