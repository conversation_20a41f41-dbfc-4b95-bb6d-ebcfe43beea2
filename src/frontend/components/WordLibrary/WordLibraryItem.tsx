/**
 * 词库项组件 - 整合版本
 * 🎯 核心价值：单个词库的展示和管理组件，整合词库操作功能
 * 📦 功能范围：词库展示、词语列表、添加删除、搜索高亮
 * 🔄 架构设计：基于词库数据的可复用组件
 */

'use client';

import React, { memo, useCallback, useMemo, useState } from 'react';
import type { WordLibrary, WordLibraryKey } from '../../../shared/types';
import { parseWordLibraryKey } from '../../../shared/utils';
import { useWordLibraryInput, useWordLibraryScroll } from '../../hooks/useWordLibrary';
import { WordInput } from './WordInput';

// ===== 组件属性 =====

interface WordLibraryItemProps {
  /** 词库数据 */
  library: WordLibrary;
  
  /** 状态 */
  isSelected?: boolean;
  isBatchSelected?: boolean;
  
  /** 事件回调 */
  onSelect?: () => void;
  onWordAdd?: (word: string) => void;
  onWordRemove?: (word: string) => void;
  onBatchSelect?: (selected: boolean) => void;
  
  /** 配置 */
  enableBatchSelection?: boolean;
  searchQuery?: string;
  showWordCount?: boolean;
  maxDisplayWords?: number;
  
  /** 样式 */
  className?: string;
  style?: React.CSSProperties;
}

// ===== 词语高亮组件 =====

const HighlightedWord = memo<{
  word: string;
  searchQuery?: string;
  onRemove?: () => void;
}>(({ word, searchQuery, onRemove }) => {
  const highlightedContent = useMemo(() => {
    if (!searchQuery || !searchQuery.trim()) {
      return word;
    }

    const query = searchQuery.toLowerCase();
    const wordLower = word.toLowerCase();
    const index = wordLower.indexOf(query);

    if (index === -1) {
      return word;
    }

    return (
      <>
        {word.slice(0, index)}
        <mark className="word-highlight">
          {word.slice(index, index + query.length)}
        </mark>
        {word.slice(index + query.length)}
      </>
    );
  }, [word, searchQuery]);

  return (
    <div className="word-item">
      <span className="word-content">{highlightedContent}</span>
      {onRemove && (
        <button
          onClick={onRemove}
          className="word-remove-btn"
          aria-label={`删除词语 ${word}`}
          title={`删除词语 ${word}`}
        >
          ×
        </button>
      )}
    </div>
  );
});

HighlightedWord.displayName = 'HighlightedWord';

// ===== 主组件 =====

export const WordLibraryItem = memo<WordLibraryItemProps>(({
  library,
  isSelected = false,
  isBatchSelected = false,
  onSelect,
  onWordAdd,
  onWordRemove,
  onBatchSelect,
  enableBatchSelection = false,
  searchQuery,
  showWordCount = true,
  maxDisplayWords = 20,
  className = '',
  style,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showInput, setShowInput] = useState(false);
  const wordsContainerRef = React.useRef<HTMLDivElement>(null);
  
  // 解析词库信息
  const { color, level } = useMemo(() => parseWordLibraryKey(library.key), [library.key]);
  
  // 使用滚动Hook
  const { scrollToBottom } = useWordLibraryScroll(wordsContainerRef);

  // 计算显示的词语
  const displayWords = useMemo(() => {
    const words = library.words;
    
    // 搜索过滤
    if (searchQuery && searchQuery.trim()) {
      const filtered = words.filter(word =>
        word.toLowerCase().includes(searchQuery.toLowerCase())
      );
      return isExpanded ? filtered : filtered.slice(0, maxDisplayWords);
    }
    
    return isExpanded ? words : words.slice(0, maxDisplayWords);
  }, [library.words, searchQuery, isExpanded, maxDisplayWords]);

  const hasMoreWords = library.words.length > maxDisplayWords;
  const hiddenWordsCount = library.words.length - displayWords.length;

  // 事件处理
  const handleSelect = useCallback(() => {
    onSelect?.();
  }, [onSelect]);

  const handleBatchSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    onBatchSelect?.(event.target.checked);
  }, [onBatchSelect]);

  const handleWordAdd = useCallback((word: string) => {
    onWordAdd?.(word);
    scrollToBottom();
  }, [onWordAdd, scrollToBottom]);

  const handleWordRemove = useCallback((word: string) => {
    onWordRemove?.(word);
  }, [onWordRemove]);

  const handleToggleExpand = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  const handleToggleInput = useCallback(() => {
    setShowInput(prev => !prev);
  }, []);

  // 计算样式
  const containerStyle: React.CSSProperties = {
    backgroundColor: library.backgroundColor,
    color: library.textColor,
    border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
    borderRadius: '8px',
    padding: '12px',
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
    position: 'relative',
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: library.words.length > 0 ? '8px' : '0',
  };

  const wordsContainerStyle: React.CSSProperties = {
    maxHeight: isExpanded ? '200px' : '120px',
    overflowY: 'auto',
    marginBottom: showInput ? '8px' : '0',
  };

  return (
    <div
      className={`word-library-item ${className} ${isSelected ? 'word-library-item--selected' : ''}`}
      style={containerStyle}
      onClick={handleSelect}
      data-testid={`word-library-item-${library.key}`}
      role="button"
      tabIndex={0}
      aria-label={`词库 ${library.displayName}，${library.words.length} 个词语`}
    >
      {/* 批量选择复选框 */}
      {enableBatchSelection && (
        <input
          type="checkbox"
          checked={isBatchSelected}
          onChange={handleBatchSelect}
          onClick={(e) => e.stopPropagation()}
          className="batch-select-checkbox"
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
          }}
          aria-label={`批量选择 ${library.displayName}`}
        />
      )}

      {/* 头部 */}
      <div style={headerStyle}>
        <div className="library-info">
          <h3 className="library-title">{library.displayName}</h3>
          {showWordCount && (
            <span className="word-count">
              {library.words.length} 个词语
            </span>
          )}
        </div>
        
        <div className="library-actions">
          {/* 添加词语按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleToggleInput();
            }}
            className="action-btn add-word-btn"
            title="添加词语"
            aria-label="添加词语"
          >
            +
          </button>
          
          {/* 展开/收起按钮 */}
          {hasMoreWords && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleToggleExpand();
              }}
              className="action-btn expand-btn"
              title={isExpanded ? '收起' : '展开'}
              aria-label={isExpanded ? '收起词语列表' : '展开词语列表'}
            >
              {isExpanded ? '↑' : '↓'}
            </button>
          )}
        </div>
      </div>

      {/* 词语输入框 */}
      {showInput && (
        <div
          className="word-input-container"
          onClick={(e) => e.stopPropagation()}
        >
          <WordInput
            libraryKey={library.key}
            placeholder="输入新词语..."
            onWordAdd={handleWordAdd}
            onCancel={() => setShowInput(false)}
            autoFocus
          />
        </div>
      )}

      {/* 词语列表 */}
      {library.words.length > 0 && (
        <div
          ref={wordsContainerRef}
          className="words-container"
          style={wordsContainerStyle}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="words-grid">
            {displayWords.map((word, index) => (
              <HighlightedWord
                key={`${word}-${index}`}
                word={word}
                searchQuery={searchQuery}
                onRemove={() => handleWordRemove(word)}
              />
            ))}
          </div>
          
          {/* 更多词语指示器 */}
          {hiddenWordsCount > 0 && (
            <div className="more-words-indicator">
              <button
                onClick={handleToggleExpand}
                className="more-words-btn"
              >
                {isExpanded ? '收起' : `还有 ${hiddenWordsCount} 个词语...`}
              </button>
            </div>
          )}
        </div>
      )}

      {/* 空状态 */}
      {library.words.length === 0 && (
        <div className="empty-library">
          <p className="empty-message">暂无词语</p>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowInput(true);
            }}
            className="add-first-word-btn"
          >
            添加第一个词语
          </button>
        </div>
      )}

      {/* 级别指示器 */}
      <div
        className="level-indicator"
        style={{
          position: 'absolute',
          bottom: '8px',
          right: '8px',
          fontSize: '10px',
          fontWeight: 'bold',
          opacity: 0.7,
        }}
      >
        L{level}
      </div>
    </div>
  );
});

WordLibraryItem.displayName = 'WordLibraryItem';

// ===== 样式常量 =====

export const WORD_LIBRARY_ITEM_STYLES = {
  container: {
    base: 'word-library-item',
    selected: 'word-library-item--selected',
    batchSelected: 'word-library-item--batch-selected',
  },
  header: {
    base: 'library-header',
    info: 'library-info',
    title: 'library-title',
    count: 'word-count',
    actions: 'library-actions',
  },
  words: {
    container: 'words-container',
    grid: 'words-grid',
    item: 'word-item',
    content: 'word-content',
    highlight: 'word-highlight',
    removeBtn: 'word-remove-btn',
  },
  actions: {
    button: 'action-btn',
    add: 'add-word-btn',
    expand: 'expand-btn',
    more: 'more-words-btn',
  },
} as const;

export default WordLibraryItem;
