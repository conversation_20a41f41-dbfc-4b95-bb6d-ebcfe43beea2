/**
 * 词库主组件 - 整合版本
 * 🎯 核心价值：统一的词库管理和展示组件，整合所有词库功能
 * 📦 功能范围：词库展示、词语管理、搜索过滤、批量操作
 * 🔄 架构设计：基于整合后的Hook和核心模块的React组件
 */

'use client';

import React, { memo, useCallback, useMemo, useState } from 'react';
import type { WordLibraryKey } from '../../../shared/types';
import { PRIMARY_COLORS, DATA_LEVELS } from '../../../shared/constants';
import { createWordLibraryKey } from '../../../shared/utils';
import {
  useWordLibraries,
  useWordLibraryStatistics,
  useWordLibrarySearch,
} from '../../hooks/useWordLibrary';
import { WordLibraryItem } from './WordLibraryItem';
import { WordInput } from './WordInput';

// ===== 组件属性 =====

interface WordLibraryProps {
  /** 自定义样式 */
  className?: string;
  style?: React.CSSProperties;
  
  /** 显示配置 */
  showStatistics?: boolean;
  showSearch?: boolean;
  showEmptyLibraries?: boolean;
  enableBatchOperations?: boolean;
  
  /** 布局配置 */
  layout?: 'grid' | 'list';
  columnsPerRow?: number;
  
  /** 事件回调 */
  onLibrarySelect?: (key: WordLibraryKey) => void;
  onWordAdd?: (key: WordLibraryKey, word: string) => void;
  onWordRemove?: (key: WordLibraryKey, word: string) => void;
}

// ===== 主组件 =====

export const WordLibrary = memo<WordLibraryProps>(({
  className = '',
  style,
  showStatistics = true,
  showSearch = true,
  showEmptyLibraries = false,
  enableBatchOperations = true,
  layout = 'grid',
  columnsPerRow = 4,
  onLibrarySelect,
  onWordAdd,
  onWordRemove,
}) => {
  const [selectedLibraries, setSelectedLibraries] = useState<Set<WordLibraryKey>>(new Set());
  
  // 使用整合后的Hook
  const {
    libraries,
    selectedLibraryKey,
    selectLibrary,
    addWord,
    removeWord,
    clearLibrary,
    reset,
  } = useWordLibraries();
  
  const stats = useWordLibraryStatistics();
  const { searchQuery, searchResults, handleSearch, clearSearch, hasResults } = useWordLibrarySearch();

  // 生成词库键列表
  const libraryKeys = useMemo(() => {
    const keys: WordLibraryKey[] = [];
    PRIMARY_COLORS.forEach(color => {
      DATA_LEVELS.forEach(level => {
        keys.push(createWordLibraryKey(color, level));
      });
    });
    return keys;
  }, []);

  // 过滤词库列表
  const filteredLibraries = useMemo(() => {
    let filtered = libraryKeys;

    // 搜索过滤
    if (hasResults) {
      filtered = searchResults.libraries.map(result => result.key);
    }

    // 空词库过滤
    if (!showEmptyLibraries) {
      filtered = filtered.filter(key => {
        const library = libraries[key];
        return library && library.words.length > 0;
      });
    }

    return filtered;
  }, [libraryKeys, libraries, showEmptyLibraries, hasResults, searchResults]);

  // 词库选择处理
  const handleLibrarySelect = useCallback((key: WordLibraryKey) => {
    selectLibrary(key);
    onLibrarySelect?.(key);
  }, [selectLibrary, onLibrarySelect]);

  // 词语添加处理
  const handleWordAdd = useCallback((key: WordLibraryKey, word: string) => {
    addWord(key, word);
    onWordAdd?.(key, word);
  }, [addWord, onWordAdd]);

  // 词语移除处理
  const handleWordRemove = useCallback((key: WordLibraryKey, word: string) => {
    removeWord(key, word);
    onWordRemove?.(key, word);
  }, [removeWord, onWordRemove]);

  // 批量操作处理
  const handleBatchClear = useCallback(() => {
    selectedLibraries.forEach(key => {
      clearLibrary(key);
    });
    setSelectedLibraries(new Set());
  }, [selectedLibraries, clearLibrary]);

  const handleBatchSelect = useCallback((key: WordLibraryKey, selected: boolean) => {
    setSelectedLibraries(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(key);
      } else {
        newSet.delete(key);
      }
      return newSet;
    });
  }, []);

  // 计算布局样式
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    ...style,
  };

  const gridStyle: React.CSSProperties = layout === 'grid' ? {
    display: 'grid',
    gridTemplateColumns: `repeat(${columnsPerRow}, 1fr)`,
    gap: '12px',
  } : {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
  };

  return (
    <div
      className={`word-library ${className}`}
      style={containerStyle}
      data-testid="word-library"
    >
      {/* 头部区域 */}
      <div className="word-library__header">
        <h2 className="word-library__title">词库管理</h2>
        
        {/* 统计信息 */}
        {showStatistics && (
          <div className="word-library__statistics">
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">总词库:</span>
                <span className="stat-value">{stats.totalLibraries}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">总词语:</span>
                <span className="stat-value">{stats.totalWords}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">平均词语:</span>
                <span className="stat-value">{stats.averageWordsPerLibrary}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">空词库:</span>
                <span className="stat-value">{stats.emptyLibraries}</span>
              </div>
            </div>
          </div>
        )}
        
        {/* 搜索区域 */}
        {showSearch && (
          <div className="word-library__search">
            <input
              type="text"
              placeholder="搜索词语..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="search-clear"
                aria-label="清除搜索"
              >
                ✕
              </button>
            )}
            {hasResults && (
              <div className="search-results-info">
                找到 {searchResults.totalMatches} 个匹配词语
              </div>
            )}
          </div>
        )}
        
        {/* 批量操作区域 */}
        {enableBatchOperations && selectedLibraries.size > 0 && (
          <div className="word-library__batch-operations">
            <span className="batch-info">
              已选择 {selectedLibraries.size} 个词库
            </span>
            <button
              onClick={handleBatchClear}
              className="batch-clear-btn"
            >
              批量清空
            </button>
            <button
              onClick={() => setSelectedLibraries(new Set())}
              className="batch-cancel-btn"
            >
              取消选择
            </button>
          </div>
        )}
      </div>

      {/* 词库网格/列表 */}
      <div
        className={`word-library__grid word-library__grid--${layout}`}
        style={gridStyle}
      >
        {filteredLibraries.map(key => {
          const library = libraries[key];
          if (!library) return null;

          return (
            <WordLibraryItem
              key={key}
              library={library}
              isSelected={selectedLibraryKey === key}
              isBatchSelected={selectedLibraries.has(key)}
              onSelect={() => handleLibrarySelect(key)}
              onWordAdd={(word) => handleWordAdd(key, word)}
              onWordRemove={(word) => handleWordRemove(key, word)}
              onBatchSelect={(selected) => handleBatchSelect(key, selected)}
              enableBatchSelection={enableBatchOperations}
              searchQuery={searchQuery}
            />
          );
        })}
      </div>

      {/* 空状态 */}
      {filteredLibraries.length === 0 && (
        <div className="word-library__empty-state">
          {hasResults ? (
            <div className="empty-search">
              <p>没有找到匹配的词语</p>
              <button onClick={clearSearch}>清除搜索</button>
            </div>
          ) : (
            <div className="empty-libraries">
              <p>没有可显示的词库</p>
              {!showEmptyLibraries && (
                <p className="empty-hint">
                  所有词库都是空的，添加一些词语或显示空词库
                </p>
              )}
            </div>
          )}
        </div>
      )}

      {/* 全局词语输入 */}
      {selectedLibraryKey && (
        <div className="word-library__global-input">
          <WordInput
            libraryKey={selectedLibraryKey}
            placeholder={`向 ${libraries[selectedLibraryKey]?.displayName} 添加词语...`}
            onWordAdd={(word) => handleWordAdd(selectedLibraryKey, word)}
          />
        </div>
      )}

      {/* 操作按钮 */}
      <div className="word-library__actions">
        <button
          onClick={reset}
          className="action-btn action-btn--danger"
        >
          重置所有词库
        </button>
        <button
          onClick={() => {
            // 导出功能
            const data = JSON.stringify(libraries, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'word-libraries.json';
            a.click();
            URL.revokeObjectURL(url);
          }}
          className="action-btn action-btn--secondary"
        >
          导出词库
        </button>
      </div>
    </div>
  );
});

WordLibrary.displayName = 'WordLibrary';

// ===== 样式常量 =====

export const WORD_LIBRARY_STYLES = {
  container: {
    base: 'word-library',
    grid: 'word-library--grid',
    list: 'word-library--list',
  },
  header: {
    base: 'word-library__header',
    title: 'word-library__title',
    statistics: 'word-library__statistics',
    search: 'word-library__search',
  },
  grid: {
    base: 'word-library__grid',
    grid: 'word-library__grid--grid',
    list: 'word-library__grid--list',
  },
  actions: {
    base: 'word-library__actions',
    button: 'action-btn',
    primary: 'action-btn--primary',
    secondary: 'action-btn--secondary',
    danger: 'action-btn--danger',
  },
} as const;

export default WordLibrary;
