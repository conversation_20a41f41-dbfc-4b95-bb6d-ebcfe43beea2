/**
 * 统一常量定义
 * 🎯 核心价值：项目中所有常量的统一定义，避免魔法数字和重复定义
 * 📦 功能范围：矩阵配置、颜色定义、尺寸规格、业务规则等
 * 🔄 架构设计：分类组织，便于维护和查找
 */

import type { BasicColorType, DataLevel, BusinessMode, MainMode, ContentMode } from '../types';

// ===== 矩阵基础常量 =====

/** 矩阵尺寸 */
export const MATRIX_SIZE = 33;

/** 总单元格数 */
export const TOTAL_CELLS = MATRIX_SIZE * MATRIX_SIZE; // 1089

/** 矩阵配置 */
export const MATRIX_CONFIG = {
  /** 矩阵尺寸 */
  SIZE: MATRIX_SIZE,
  /** 单元格基础尺寸 */
  CELL_SIZE: 33,
  /** 增强单元格尺寸（1级格子） */
  ENHANCED_CELL_SIZE: 39,
  /** 单元格间距 */
  CELL_GAP: 1,
  /** 容器内边距 */
  CONTAINER_PADDING: 6,
  /** 基础矩阵尺寸（33 * 34px） */
  BASE_SIZE: 1122,
  /** 容器扩展尺寸（为1级格子预留空间） */
  CONTAINER_EXPANSION: 12,
} as const;

// ===== 颜色系统常量 =====

/** 基础颜色列表 */
export const BASIC_COLORS: BasicColorType[] = [
  'red', 'blue', 'green', 'yellow',
  'purple', 'orange', 'pink', 'cyan',
  'black', 'white', 'gray', 'brown'
];

/** 主要颜色（8色系统） */
export const PRIMARY_COLORS: BasicColorType[] = [
  'red', 'blue', 'green', 'yellow',
  'purple', 'orange', 'pink', 'cyan'
];

/** 颜色显示名称映射 */
export const COLOR_DISPLAY_NAMES: Record<BasicColorType, string> = {
  red: '红色',
  blue: '蓝色', 
  green: '绿色',
  yellow: '黄色',
  purple: '紫色',
  orange: '橙色',
  pink: '粉色',
  cyan: '青色',
  black: '黑色',
  white: '白色',
  gray: '灰色',
  brown: '棕色',
};

/** 颜色十六进制值映射 */
export const COLOR_HEX_VALUES: Record<BasicColorType, string> = {
  red: '#ef4444',
  blue: '#3b82f6',
  green: '#22c55e',
  yellow: '#eab308',
  purple: '#a855f7',
  orange: '#f97316',
  pink: '#ec4899',
  cyan: '#06b6d4',
  black: '#000000',
  white: '#ffffff',
  gray: '#6b7280',
  brown: '#b45309',
};

/** Tailwind CSS类名映射 */
export const COLOR_TAILWIND_CLASSES: Record<BasicColorType, string> = {
  red: 'bg-red-500',
  blue: 'bg-blue-500',
  green: 'bg-green-500',
  yellow: 'bg-yellow-500',
  purple: 'bg-purple-500',
  orange: 'bg-orange-500',
  pink: 'bg-pink-500',
  cyan: 'bg-cyan-500',
  black: 'bg-black',
  white: 'bg-white',
  gray: 'bg-gray-500',
  brown: 'bg-amber-700',
};

// ===== 数据级别常量 =====

/** 数据级别列表 */
export const DATA_LEVELS: DataLevel[] = [1, 2, 3, 4];

/** 级别显示名称映射 */
export const LEVEL_DISPLAY_NAMES: Record<DataLevel, string> = {
  1: '一级',
  2: '二级', 
  3: '三级',
  4: '四级',
};

// ===== 业务模式常量 =====

/** 主模式列表 */
export const MAIN_MODES: MainMode[] = ['color', 'number'];

/** 内容模式列表 */
export const CONTENT_MODES: ContentMode[] = ['word', 'number', 'coordinate'];

/** 业务模式列表 */
export const BUSINESS_MODES: BusinessMode[] = [
  'coordinate', 'color', 'level', 'word', 'color-word', 'number-word'
];

/** 模式显示名称映射 */
export const MODE_DISPLAY_NAMES: Record<BusinessMode, string> = {
  coordinate: '坐标模式',
  color: '颜色模式',
  level: '级别模式',
  word: '词语模式',
  'color-word': '颜色+词语模式',
  'number-word': '数字+词语模式',
};

// ===== 性能优化常量 =====

/** 防抖延迟配置 */
export const DEBOUNCE_DELAYS = {
  /** 用户输入防抖 */
  USER_INPUT: 300,
  /** 搜索防抖 */
  SEARCH: 500,
  /** 滚动防抖 */
  SCROLL: 100,
  /** 窗口大小调整防抖 */
  RESIZE: 250,
} as const;

/** 节流间隔配置 */
export const THROTTLE_INTERVALS = {
  /** 滚动节流 */
  SCROLL: 16,
  /** 鼠标移动节流 */
  MOUSE_MOVE: 16,
  /** 窗口大小调整节流 */
  RESIZE: 100,
  /** API请求节流 */
  API_REQUEST: 1000,
} as const;

/** 缓存配置 */
export const CACHE_CONFIG = {
  /** 最大缓存条目数 */
  MAX_ENTRIES: 1000,
  /** 缓存过期时间（毫秒） */
  EXPIRY_TIME: 5 * 60 * 1000, // 5分钟
  /** 清理间隔（毫秒） */
  CLEANUP_INTERVAL: 60 * 1000, // 1分钟
} as const;

// ===== UI常量 =====

/** 动画持续时间 */
export const ANIMATION_DURATIONS = {
  /** 快速动画 */
  FAST: 150,
  /** 正常动画 */
  NORMAL: 300,
  /** 慢速动画 */
  SLOW: 500,
} as const;

/** Z-index层级 */
export const Z_INDEX = {
  /** 基础层 */
  BASE: 1,
  /** 下拉菜单 */
  DROPDOWN: 1000,
  /** 模态框 */
  MODAL: 1050,
  /** 提示框 */
  TOOLTIP: 1100,
  /** 通知 */
  NOTIFICATION: 1200,
} as const;

/** 断点配置 */
export const BREAKPOINTS = {
  /** 小屏幕 */
  SM: 640,
  /** 中等屏幕 */
  MD: 768,
  /** 大屏幕 */
  LG: 1024,
  /** 超大屏幕 */
  XL: 1280,
  /** 2K屏幕 */
  '2XL': 1536,
} as const;

// ===== 验证规则常量 =====

/** 词语验证规则 */
export const WORD_VALIDATION = {
  /** 最小长度 */
  MIN_LENGTH: 1,
  /** 最大长度 */
  MAX_LENGTH: 10,
  /** 允许的字符正则 */
  ALLOWED_CHARS: /^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/,
  /** 禁止的词语 */
  FORBIDDEN_WORDS: ['测试', 'test', '删除', 'delete'],
} as const;

/** 坐标验证规则 */
export const COORDINATE_VALIDATION = {
  /** 最小坐标值 */
  MIN_VALUE: 0,
  /** 最大坐标值 */
  MAX_VALUE: MATRIX_SIZE - 1,
} as const;

// ===== API常量 =====

/** API端点 */
export const API_ENDPOINTS = {
  /** 健康检查 */
  HEALTH: '/api/health',
  /** 矩阵数据 */
  MATRIX: '/api/matrix',
  /** 词库数据 */
  WORD_LIBRARY: '/api/word-library',
} as const;

/** HTTP状态码 */
export const HTTP_STATUS = {
  /** 成功 */
  OK: 200,
  /** 创建成功 */
  CREATED: 201,
  /** 无内容 */
  NO_CONTENT: 204,
  /** 错误请求 */
  BAD_REQUEST: 400,
  /** 未授权 */
  UNAUTHORIZED: 401,
  /** 禁止访问 */
  FORBIDDEN: 403,
  /** 未找到 */
  NOT_FOUND: 404,
  /** 服务器错误 */
  INTERNAL_SERVER_ERROR: 500,
} as const;

// ===== 本地存储键常量 =====

/** 本地存储键 */
export const STORAGE_KEYS = {
  /** 矩阵数据 */
  MATRIX_DATA: 'cube1_matrix_data',
  /** 矩阵配置 */
  MATRIX_CONFIG: 'cube1_matrix_config',
  /** 词库数据 */
  WORD_LIBRARIES: 'cube1_word_libraries',
  /** 用户偏好 */
  USER_PREFERENCES: 'cube1_user_preferences',
  /** 应用状态 */
  APP_STATE: 'cube1_app_state',
} as const;

// ===== 错误消息常量 =====

/** 错误消息 */
export const ERROR_MESSAGES = {
  /** 网络错误 */
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  /** 数据加载失败 */
  DATA_LOAD_FAILED: '数据加载失败，请刷新页面重试',
  /** 保存失败 */
  SAVE_FAILED: '保存失败，请重试',
  /** 无效输入 */
  INVALID_INPUT: '输入内容无效，请检查后重试',
  /** 权限不足 */
  PERMISSION_DENIED: '权限不足，无法执行此操作',
} as const;

/** 成功消息 */
export const SUCCESS_MESSAGES = {
  /** 保存成功 */
  SAVE_SUCCESS: '保存成功',
  /** 删除成功 */
  DELETE_SUCCESS: '删除成功',
  /** 更新成功 */
  UPDATE_SUCCESS: '更新成功',
  /** 导入成功 */
  IMPORT_SUCCESS: '导入成功',
  /** 导出成功 */
  EXPORT_SUCCESS: '导出成功',
} as const;

// ===== 默认值常量 =====

/** 默认矩阵配置 */
export const DEFAULT_MATRIX_CONFIG = {
  mode: 'color-word' as BusinessMode,
  mainMode: 'color' as MainMode,
  contentMode: 'word' as ContentMode,
  isColorMode: true,
  isWordMode: true,
  isNumberMode: false,
  isCoordinateMode: false,
} as const;

/** 默认颜色值 */
export const DEFAULT_COLOR_VALUES: Record<BasicColorType, number> = {
  red: 1, blue: 2, green: 3, yellow: 4,
  purple: 5, orange: 6, pink: 7, cyan: 8,
  black: 9, white: 0, gray: 10, brown: 11,
};
