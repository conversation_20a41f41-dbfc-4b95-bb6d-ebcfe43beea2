/**
 * 稳定的词库滚动管理Hook
 * 🎯 核心价值：提供稳定的滚动位置管理，只在真正需要时触发滚动
 * 📦 功能范围：滚动位置保存、恢复、智能滑动控制
 * 🔄 架构设计：基于ref的位置管理，完全隔离于React状态系统
 */

'use client';

import { useCallback, useEffect, useRef } from 'react';
import type { WordLibraryKey } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

interface ScrollPosition {
  top: number;
  timestamp: number;
  isUserScroll: boolean; // 区分用户滚动和程序滚动
}

interface ScrollToLibraryOptions {
  behavior?: ScrollBehavior;
  block?: ScrollLogicalPosition;
  force?: boolean;
  debug?: boolean;
}

interface UseStableWordLibraryScrollReturn {
  /** 滚动到指定词库 */
  scrollToLibrary: (libraryKey: WordLibraryKey, options?: ScrollToLibraryOptions) => boolean;
  /** 保存当前滚动位置 */
  savePosition: () => void;
  /** 恢复滚动位置 */
  restorePosition: () => void;
  /** 重置滚动状态 */
  resetScrollState: () => void;
  /** 设置滚动容器引用 */
  setScrollContainer: (container: HTMLElement | null) => void;
}

// ===== 主Hook =====

/**
 * 稳定的词库滚动管理Hook
 */
export const useStableWordLibraryScroll = (
  debug = false
): UseStableWordLibraryScrollReturn => {
  // 使用ref存储状态，避免React重新渲染影响
  const scrollContainerRef = useRef<HTMLElement | null>(null);
  const savedPositionRef = useRef<ScrollPosition>({
    top: 0,
    timestamp: 0,
    isUserScroll: true
  });
  const isScrollingRef = useRef(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActiveLibraryRef = useRef<WordLibraryKey | null>(null);

  /**
   * 设置滚动容器
   */
  const setScrollContainer = useCallback((container: HTMLElement | null) => {
    if (debug) {
      console.log('[useStableWordLibraryScroll] 设置滚动容器:', container);
    }
    scrollContainerRef.current = container;
  }, [debug]);

  /**
   * 保存当前滚动位置
   */
  const savePosition = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      savedPositionRef.current = {
        top: container.scrollTop,
        timestamp: Date.now(),
        isUserScroll: !isScrollingRef.current
      };
      
      if (debug) {
        console.log('[useStableWordLibraryScroll] 保存位置:', savedPositionRef.current);
      }
    }
  }, [debug]);

  /**
   * 恢复滚动位置
   */
  const restorePosition = useCallback(() => {
    const container = scrollContainerRef.current;
    const savedPosition = savedPositionRef.current;
    
    if (container && savedPosition.top > 0) {
      // 检查时间差，避免恢复过旧的位置
      const timeDiff = Date.now() - savedPosition.timestamp;
      if (timeDiff < 10000) { // 10秒内的位置才恢复
        isScrollingRef.current = true;
        container.scrollTop = savedPosition.top;
        
        // 延迟重置滚动标记
        setTimeout(() => {
          isScrollingRef.current = false;
        }, 100);
        
        if (debug) {
          console.log('[useStableWordLibraryScroll] 恢复位置:', savedPosition.top);
        }
      }
    }
  }, [debug]);

  /**
   * 滚动到指定词库
   */
  const scrollToLibrary = useCallback((
    libraryKey: WordLibraryKey,
    options: ScrollToLibraryOptions = {}
  ): boolean => {
    const {
      behavior = 'smooth',
      block = 'center',
      force = false,
      debug: optionDebug = false
    } = options;

    const enableDebug = debug || optionDebug;

    if (enableDebug) {
      console.log('[useStableWordLibraryScroll] 开始滚动到词库:', {
        libraryKey,
        lastActiveLibrary: lastActiveLibraryRef.current,
        force
      });
    }

    // 防重复滚动：如果目标词库与上次相同且不强制，则跳过
    if (!force && lastActiveLibraryRef.current === libraryKey) {
      if (enableDebug) {
        console.log('[useStableWordLibraryScroll] 跳过重复滚动');
      }
      return false;
    }

    const container = scrollContainerRef.current;
    if (!container) {
      if (enableDebug) {
        console.warn('[useStableWordLibraryScroll] 滚动容器未找到');
      }
      return false;
    }

    // 查找目标元素
    const targetElement = container.querySelector(`[data-word-library="${libraryKey}"]`) as HTMLElement;
    if (!targetElement) {
      if (enableDebug) {
        console.warn('[useStableWordLibraryScroll] 目标词库元素未找到:', libraryKey);
      }
      return false;
    }

    // 计算滚动位置
    const containerRect = container.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();
    const containerScrollTop = container.scrollTop;
    
    let targetScrollTop: number;
    
    switch (block) {
      case 'start':
        targetScrollTop = containerScrollTop + targetRect.top - containerRect.top;
        break;
      case 'end':
        targetScrollTop = containerScrollTop + targetRect.bottom - containerRect.bottom;
        break;
      case 'center':
      default:
        targetScrollTop = containerScrollTop + targetRect.top - containerRect.top - 
                         (containerRect.height - targetRect.height) / 2;
        break;
    }

    // 检查是否需要滚动
    const scrollDelta = Math.abs(targetScrollTop - containerScrollTop);
    if (scrollDelta < 10 && !force) {
      if (enableDebug) {
        console.log('[useStableWordLibraryScroll] 位置已正确，无需滚动');
      }
      lastActiveLibraryRef.current = libraryKey;
      return false;
    }

    // 执行滚动
    isScrollingRef.current = true;
    lastActiveLibraryRef.current = libraryKey;
    
    container.scrollTo({
      top: targetScrollTop,
      behavior
    });

    // 延迟重置滚动标记
    setTimeout(() => {
      isScrollingRef.current = false;
    }, behavior === 'smooth' ? 500 : 100);

    if (enableDebug) {
      console.log('[useStableWordLibraryScroll] 滚动执行完成:', {
        from: containerScrollTop,
        to: targetScrollTop,
        delta: scrollDelta
      });
    }

    return true;
  }, [debug]);

  /**
   * 重置滚动状态
   */
  const resetScrollState = useCallback(() => {
    lastActiveLibraryRef.current = null;
    isScrollingRef.current = false;
    
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = null;
    }
    
    if (debug) {
      console.log('[useStableWordLibraryScroll] 滚动状态已重置');
    }
  }, [debug]);

  /**
   * 监听滚动事件，自动保存位置
   */
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      // 清除之前的定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // 防抖保存位置
      scrollTimeoutRef.current = setTimeout(() => {
        savePosition();
      }, 150);
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [savePosition]);

  /**
   * 组件卸载时保存位置
   */
  useEffect(() => {
    return () => {
      savePosition();
    };
  }, [savePosition]);

  return {
    scrollToLibrary,
    savePosition,
    restorePosition,
    resetScrollState,
    setScrollContainer
  };
};

// ===== 导出类型 =====

export type {
  ScrollPosition,
  ScrollToLibraryOptions,
  UseStableWordLibraryScrollReturn
};
