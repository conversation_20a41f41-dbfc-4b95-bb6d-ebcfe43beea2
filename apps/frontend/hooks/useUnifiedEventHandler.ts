/**
 * 统一事件处理Hook - 重构版本
 * 🎯 核心价值：整合所有事件处理逻辑，消除重复代码，提供一致的交互体验
 * 📦 功能范围：坐标事件、键盘导航、焦点管理、防抖节流、事件委托
 * 🔄 架构设计：基于统一事件服务的React Hook封装
 */

'use client';

import { useCallback, useEffect, useRef } from 'react';
import {
  EventHandlingService,
  getEventService,
  createCoordinateHandler,
  createKeyboardNavigationHandler,
  createMatrixCellHandlers,
  type EventHandlerOptions,
  type CoordinateEventHandler,
  type CoordinateEventHandlers,
  type KeyboardNavigationOptions,
  type FocusManagementOptions
} from '@/core/services/EventHandlingService';
import type { Coordinate } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

export interface UseUnifiedEventHandlerOptions extends EventHandlerOptions {
  /** 是否启用键盘导航 */
  enableKeyboardNavigation?: boolean;
  /** 键盘导航选项 */
  keyboardOptions?: KeyboardNavigationOptions;
  /** 是否启用焦点管理 */
  enableFocusManagement?: boolean;
  /** 焦点管理选项 */
  focusOptions?: FocusManagementOptions;
  /** 是否启用事件委托 */
  enableEventDelegation?: boolean;
  /** 委托容器选择器 */
  delegationContainer?: string;
}

export interface UseUnifiedEventHandlerReturn {
  /** 创建坐标事件处理器 */
  createCoordinateHandler: <T extends Event>(
    handler: CoordinateEventHandler<T>,
    options?: EventHandlerOptions
  ) => (event: T) => void;
  /** 创建矩阵单元格事件处理器 */
  createMatrixCellHandlers: (
    x: number,
    y: number,
    handlers: {
      onClick?: (x: number, y: number, event: MouseEvent) => void;
      onDoubleClick?: (x: number, y: number, event: MouseEvent) => void;
      onMouseEnter?: (x: number, y: number, event: MouseEvent) => void;
      onMouseLeave?: (x: number, y: number, event: MouseEvent) => void;
      onFocus?: (x: number, y: number, event: FocusEvent) => void;
      onBlur?: (x: number, y: number, event: FocusEvent) => void;
    }
  ) => Record<string, (event: Event) => void>;
  /** 创建键盘导航处理器 */
  createKeyboardHandler: (handlers: {
    onArrowUp?: () => void;
    onArrowDown?: () => void;
    onArrowLeft?: () => void;
    onArrowRight?: () => void;
    onEnter?: () => void;
    onEscape?: () => void;
    onTab?: () => void;
    onCustomKey?: (key: string) => void;
  }) => (event: KeyboardEvent) => void;
  /** 管理焦点 */
  manageFocus: (element: HTMLElement) => (() => void) | void;
  /** 清理事件处理器 */
  cleanup: () => void;
}

// ===== 主Hook =====

/**
 * 统一事件处理Hook
 */
export const useUnifiedEventHandler = (
  options: UseUnifiedEventHandlerOptions = {}
): UseUnifiedEventHandlerReturn => {
  const {
    enableKeyboardNavigation = false,
    keyboardOptions = {},
    enableFocusManagement = false,
    focusOptions = {},
    enableEventDelegation = false,
    delegationContainer,
    debug = false,
    ...eventOptions
  } = options;

  const eventService = useRef(getEventService());
  const cleanupFunctions = useRef<Array<() => void>>([]);

  // 设置调试模式
  useEffect(() => {
    eventService.current.setDebugMode(debug);
  }, [debug]);

  // 创建坐标事件处理器
  const createCoordinateHandlerCallback = useCallback(<T extends Event>(
    handler: CoordinateEventHandler<T>,
    handlerOptions?: EventHandlerOptions
  ) => {
    const finalOptions = { ...eventOptions, ...handlerOptions };
    return eventService.current.createCoordinateHandler(handler, finalOptions);
  }, [eventOptions]);

  // 创建矩阵单元格事件处理器
  const createMatrixCellHandlersCallback = useCallback((
    x: number,
    y: number,
    handlers: Parameters<EventHandlingService['createMatrixCellHandlers']>[2]
  ) => {
    return eventService.current.createMatrixCellHandlers(x, y, handlers, eventOptions);
  }, [eventOptions]);

  // 创建键盘导航处理器
  const createKeyboardHandlerCallback = useCallback((
    handlers: Parameters<EventHandlingService['createKeyboardNavigationHandler']>[0]
  ) => {
    return eventService.current.createKeyboardNavigationHandler(handlers, keyboardOptions);
  }, [keyboardOptions]);

  // 管理焦点
  const manageFocusCallback = useCallback((element: HTMLElement) => {
    if (enableFocusManagement) {
      return eventService.current.manageFocus(element, focusOptions);
    }
  }, [enableFocusManagement, focusOptions]);

  // 清理函数
  const cleanup = useCallback(() => {
    // 执行所有清理函数
    cleanupFunctions.current.forEach(cleanupFn => {
      try {
        cleanupFn();
      } catch (error) {
        console.error('[useUnifiedEventHandler] 清理函数执行失败:', error);
      }
    });
    cleanupFunctions.current.length = 0;

    // 清理事件服务
    eventService.current.cleanup();
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    createCoordinateHandler: createCoordinateHandlerCallback,
    createMatrixCellHandlers: createMatrixCellHandlersCallback,
    createKeyboardHandler: createKeyboardHandlerCallback,
    manageFocus: manageFocusCallback,
    cleanup
  };
};

// ===== 专用Hook =====

/**
 * 矩阵单元格事件处理Hook
 */
export const useMatrixCellEventHandler = (
  x: number,
  y: number,
  handlers: {
    onClick?: (x: number, y: number, event: MouseEvent) => void;
    onDoubleClick?: (x: number, y: number, event: MouseEvent) => void;
    onMouseEnter?: (x: number, y: number, event: MouseEvent) => void;
    onMouseLeave?: (x: number, y: number, event: MouseEvent) => void;
    onFocus?: (x: number, y: number, event: FocusEvent) => void;
    onBlur?: (x: number, y: number, event: FocusEvent) => void;
  },
  options: EventHandlerOptions = {}
) => {
  const { createMatrixCellHandlers } = useUnifiedEventHandler(options);
  
  return useCallback(() => {
    return createMatrixCellHandlers(x, y, handlers);
  }, [createMatrixCellHandlers, x, y, handlers]);
};

/**
 * 键盘导航Hook
 */
export const useKeyboardNavigation = (
  handlers: {
    onArrowUp?: () => void;
    onArrowDown?: () => void;
    onArrowLeft?: () => void;
    onArrowRight?: () => void;
    onEnter?: () => void;
    onEscape?: () => void;
    onTab?: () => void;
    onCustomKey?: (key: string) => void;
  },
  options: KeyboardNavigationOptions = {},
  enabled: boolean = true
) => {
  const { createKeyboardHandler } = useUnifiedEventHandler({
    enableKeyboardNavigation: enabled,
    keyboardOptions: options
  });

  const keyboardHandler = useCallback(() => {
    return createKeyboardHandler(handlers);
  }, [createKeyboardHandler, handlers]);

  // 自动绑定到document
  useEffect(() => {
    if (!enabled) return;

    const handler = keyboardHandler();
    document.addEventListener('keydown', handler);

    return () => {
      document.removeEventListener('keydown', handler);
    };
  }, [keyboardHandler, enabled]);

  return keyboardHandler;
};

/**
 * 焦点管理Hook
 */
export const useFocusManagement = (
  elementRef: React.RefObject<HTMLElement>,
  options: FocusManagementOptions = {}
) => {
  const { manageFocus } = useUnifiedEventHandler({
    enableFocusManagement: true,
    focusOptions: options
  });

  useEffect(() => {
    if (elementRef.current) {
      return manageFocus(elementRef.current);
    }
  }, [manageFocus, elementRef]);
};

/**
 * 坐标事件Hook
 */
export const useCoordinateEvents = (
  handlers: CoordinateEventHandlers,
  options: EventHandlerOptions = {}
) => {
  const { createCoordinateHandler } = useUnifiedEventHandler(options);

  return useCallback(() => {
    const result: Record<string, (event: Event) => void> = {};

    if (handlers.onClick) {
      result.onClick = createCoordinateHandler(handlers.onClick);
    }
    if (handlers.onDoubleClick) {
      result.onDoubleClick = createCoordinateHandler(handlers.onDoubleClick);
    }
    if (handlers.onMouseEnter) {
      result.onMouseEnter = createCoordinateHandler(handlers.onMouseEnter);
    }
    if (handlers.onMouseLeave) {
      result.onMouseLeave = createCoordinateHandler(handlers.onMouseLeave);
    }
    if (handlers.onFocus) {
      result.onFocus = createCoordinateHandler(handlers.onFocus);
    }
    if (handlers.onBlur) {
      result.onBlur = createCoordinateHandler(handlers.onBlur);
    }

    return result;
  }, [createCoordinateHandler, handlers]);
};

/**
 * 防抖事件Hook
 */
export const useDebouncedEventHandler = <T extends Event>(
  handler: (event: T) => void,
  delay: number = 150
) => {
  const { createCoordinateHandler } = useUnifiedEventHandler({
    enableDebounce: true,
    debounceDelay: delay
  });

  return useCallback((coordinate: Coordinate, event: T) => {
    handler(event);
  }, [handler]);
};

/**
 * 节流事件Hook
 */
export const useThrottledEventHandler = <T extends Event>(
  handler: (event: T) => void,
  delay: number = 100
) => {
  const { createCoordinateHandler } = useUnifiedEventHandler({
    enableThrottle: true,
    throttleDelay: delay
  });

  return useCallback((coordinate: Coordinate, event: T) => {
    handler(event);
  }, [handler]);
};

/**
 * 事件委托Hook
 */
export const useEventDelegation = (
  containerRef: React.RefObject<HTMLElement>,
  eventType: string,
  targetSelector: string,
  handler: (target: HTMLElement, event: Event) => void,
  options: EventHandlerOptions = {}
) => {
  const eventService = useRef(getEventService());

  useEffect(() => {
    if (!containerRef.current) return;

    const cleanup = eventService.current.createEventDelegation(
      containerRef.current,
      eventType,
      targetSelector,
      handler,
      options
    );

    return cleanup;
  }, [containerRef, eventType, targetSelector, handler, options]);
};

// ===== 导出类型 =====
export type {
  UseUnifiedEventHandlerOptions,
  UseUnifiedEventHandlerReturn
};
