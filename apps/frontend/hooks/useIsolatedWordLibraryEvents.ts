/**
 * 隔离的词库事件处理Hook
 * 🎯 核心价值：隔离词库操作事件，防止触发不必要的组件重新渲染
 * 📦 功能范围：词库操作事件处理、状态更新隔离、性能优化
 * 🔄 架构设计：基于事件委托和局部更新的隔离机制
 */

'use client';

import { useCallback, useRef } from 'react';
import type { WordLibraryKey, WordValidationResult } from '@/core/matrix/MatrixTypes';
import { useWordLibraryActions } from '@/hooks/useOptimizedWordLibraryStore';

// ===== 类型定义 =====

interface WordLibraryEventHandlers {
  /** 添加词语事件处理器 */
  handleAddWord: (libraryKey: WordLibraryKey, text: string) => Promise<WordValidationResult>;
  /** 删除词语事件处理器 */
  handleRemoveWord: (libraryKey: WordLibraryKey, wordId: string) => Promise<boolean>;
  /** 切换折叠状态事件处理器 */
  handleToggleCollapse: (libraryKey: WordLibraryKey) => Promise<void>;
  /** 批量添加词语事件处理器 */
  handleBatchAddWords: (libraryKey: WordLibraryKey, texts: string[]) => Promise<WordValidationResult[]>;
  /** 清空词库事件处理器 */
  handleClearLibrary: (libraryKey: WordLibraryKey) => Promise<void>;
}

interface EventProcessingOptions {
  /** 是否启用防抖 */
  debounce?: boolean;
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number;
  /** 是否启用批处理 */
  batch?: boolean;
  /** 批处理延迟时间（毫秒） */
  batchDelay?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

// ===== 主Hook =====

/**
 * 隔离的词库事件处理Hook
 */
export const useIsolatedWordLibraryEvents = (
  options: EventProcessingOptions = {}
): WordLibraryEventHandlers => {
  const {
    debounce = true,
    debounceDelay = 300,
    batch = true,
    batchDelay = 100,
    debug = false
  } = options;

  // 获取词库操作方法
  const {
    addWord,
    removeWord,
    toggleLibraryCollapse,
    addWords,
    clearLibrary
  } = useWordLibraryActions();

  // 防抖和批处理状态
  const debounceTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const batchQueueRef = useRef<Map<string, any[]>>(new Map());
  const batchTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  /**
   * 清理定时器
   */
  const clearTimer = useCallback((key: string, type: 'debounce' | 'batch') => {
    const timers = type === 'debounce' ? debounceTimersRef.current : batchTimersRef.current;
    const timer = timers.get(key);
    if (timer) {
      clearTimeout(timer);
      timers.delete(key);
    }
  }, []);

  /**
   * 添加词语事件处理器
   */
  const handleAddWord = useCallback(async (
    libraryKey: WordLibraryKey,
    text: string
  ): Promise<WordValidationResult> => {
    return new Promise((resolve) => {
      const key = `add-${libraryKey}`;

      if (debug) {
        console.log('[useIsolatedWordLibraryEvents] 处理添加词语:', { libraryKey, text });
      }

      const executeAdd = () => {
        try {
          const result = addWord(libraryKey, text);
          resolve(result);
          
          if (debug) {
            console.log('[useIsolatedWordLibraryEvents] 添加词语完成:', result);
          }
        } catch (error) {
          console.error('[useIsolatedWordLibraryEvents] 添加词语失败:', error);
          resolve({
            isValid: false,
            errors: ['添加词语时发生错误'],
            isDuplicate: false,
            duplicateLibraries: []
          });
        }
      };

      if (debounce) {
        clearTimer(key, 'debounce');
        const timer = setTimeout(executeAdd, debounceDelay);
        debounceTimersRef.current.set(key, timer);
      } else {
        executeAdd();
      }
    });
  }, [addWord, debounce, debounceDelay, debug, clearTimer]);

  /**
   * 删除词语事件处理器
   */
  const handleRemoveWord = useCallback(async (
    libraryKey: WordLibraryKey,
    wordId: string
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const key = `remove-${libraryKey}-${wordId}`;

      if (debug) {
        console.log('[useIsolatedWordLibraryEvents] 处理删除词语:', { libraryKey, wordId });
      }

      const executeRemove = () => {
        try {
          const result = removeWord(libraryKey, wordId);
          resolve(result);
          
          if (debug) {
            console.log('[useIsolatedWordLibraryEvents] 删除词语完成:', result);
          }
        } catch (error) {
          console.error('[useIsolatedWordLibraryEvents] 删除词语失败:', error);
          resolve(false);
        }
      };

      if (debounce) {
        clearTimer(key, 'debounce');
        const timer = setTimeout(executeRemove, debounceDelay);
        debounceTimersRef.current.set(key, timer);
      } else {
        executeRemove();
      }
    });
  }, [removeWord, debounce, debounceDelay, debug, clearTimer]);

  /**
   * 切换折叠状态事件处理器
   */
  const handleToggleCollapse = useCallback(async (
    libraryKey: WordLibraryKey
  ): Promise<void> => {
    return new Promise((resolve) => {
      const key = `toggle-${libraryKey}`;

      if (debug) {
        console.log('[useIsolatedWordLibraryEvents] 处理切换折叠:', { libraryKey });
      }

      const executeToggle = () => {
        try {
          toggleLibraryCollapse(libraryKey);
          resolve();
          
          if (debug) {
            console.log('[useIsolatedWordLibraryEvents] 切换折叠完成');
          }
        } catch (error) {
          console.error('[useIsolatedWordLibraryEvents] 切换折叠失败:', error);
          resolve();
        }
      };

      if (debounce) {
        clearTimer(key, 'debounce');
        const timer = setTimeout(executeToggle, debounceDelay);
        debounceTimersRef.current.set(key, timer);
      } else {
        executeToggle();
      }
    });
  }, [toggleLibraryCollapse, debounce, debounceDelay, debug, clearTimer]);

  /**
   * 批量添加词语事件处理器
   */
  const handleBatchAddWords = useCallback(async (
    libraryKey: WordLibraryKey,
    texts: string[]
  ): Promise<WordValidationResult[]> => {
    return new Promise((resolve) => {
      const key = `batch-add-${libraryKey}`;

      if (debug) {
        console.log('[useIsolatedWordLibraryEvents] 处理批量添加词语:', { libraryKey, count: texts.length });
      }

      const executeBatchAdd = () => {
        try {
          const results = addWords(libraryKey, texts);
          resolve(results);
          
          if (debug) {
            console.log('[useIsolatedWordLibraryEvents] 批量添加词语完成:', results);
          }
        } catch (error) {
          console.error('[useIsolatedWordLibraryEvents] 批量添加词语失败:', error);
          resolve(texts.map(() => ({
            isValid: false,
            errors: ['批量添加词语时发生错误'],
            isDuplicate: false,
            duplicateLibraries: []
          })));
        }
      };

      if (batch) {
        // 将词语添加到批处理队列
        const queue = batchQueueRef.current.get(key) || [];
        queue.push(...texts);
        batchQueueRef.current.set(key, queue);

        clearTimer(key, 'batch');
        const timer = setTimeout(() => {
          const queuedTexts = batchQueueRef.current.get(key) || [];
          batchQueueRef.current.delete(key);
          
          if (queuedTexts.length > 0) {
            executeBatchAdd();
          } else {
            resolve([]);
          }
        }, batchDelay);
        batchTimersRef.current.set(key, timer);
      } else {
        executeBatchAdd();
      }
    });
  }, [addWords, batch, batchDelay, debug, clearTimer]);

  /**
   * 清空词库事件处理器
   */
  const handleClearLibrary = useCallback(async (
    libraryKey: WordLibraryKey
  ): Promise<void> => {
    return new Promise((resolve) => {
      const key = `clear-${libraryKey}`;

      if (debug) {
        console.log('[useIsolatedWordLibraryEvents] 处理清空词库:', { libraryKey });
      }

      const executeClear = () => {
        try {
          clearLibrary(libraryKey);
          resolve();
          
          if (debug) {
            console.log('[useIsolatedWordLibraryEvents] 清空词库完成');
          }
        } catch (error) {
          console.error('[useIsolatedWordLibraryEvents] 清空词库失败:', error);
          resolve();
        }
      };

      if (debounce) {
        clearTimer(key, 'debounce');
        const timer = setTimeout(executeClear, debounceDelay);
        debounceTimersRef.current.set(key, timer);
      } else {
        executeClear();
      }
    });
  }, [clearLibrary, debounce, debounceDelay, debug, clearTimer]);

  return {
    handleAddWord,
    handleRemoveWord,
    handleToggleCollapse,
    handleBatchAddWords,
    handleClearLibrary
  };
};

// ===== 导出类型 =====

export type {
  WordLibraryEventHandlers,
  EventProcessingOptions
};
