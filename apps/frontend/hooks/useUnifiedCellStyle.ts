/**
 * 统一单元格样式Hook - 重构版本
 * 🎯 核心价值：整合所有样式和类名计算逻辑，消除重复代码，提供一致的样式体验
 * 📦 功能范围：样式计算、类名生成、缓存优化、主题支持、响应式设计
 * 🔄 架构设计：基于统一样式服务的React Hook封装
 */

'use client';

import { useCallback, useMemo } from 'react';
import {
  StyleCalculationService,
  getStyleService,
  calculateCellStyle,
  calculateCellClassName,
  calculateCellStyleOnly,
  createThemeStyles,
  createResponsiveStyles,
  type StyleCalculationOptions,
  type StyleCalculationResult,
  type StyleTheme
} from '@/core/services/StyleCalculationService';
import type { 
  CellData, 
  CellRenderData, 
  Coordinate, 
  MatrixConfig 
} from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

export interface UseUnifiedCellStyleOptions extends Omit<StyleCalculationOptions, 'coordinate'> {
  /** 单元格坐标 */
  coordinate: Coordinate;
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 响应式断点 */
  breakpoint?: 'sm' | 'md' | 'lg' | 'xl';
  /** 是否启用调试模式 */
  debug?: boolean;
}

export interface UseUnifiedCellStyleReturn extends StyleCalculationResult {
  /** 更新样式配置 */
  updateOptions: (newOptions: Partial<UseUnifiedCellStyleOptions>) => void;
  /** 清除缓存 */
  clearCache: () => void;
  /** 获取主题样式 */
  getThemeStyles: () => React.CSSProperties;
  /** 获取响应式样式 */
  getResponsiveStyles: () => React.CSSProperties;
  /** 重新计算样式 */
  recalculate: () => StyleCalculationResult;
}

// ===== 主Hook =====

/**
 * 统一单元格样式Hook
 */
export const useUnifiedCellStyle = (
  options: UseUnifiedCellStyleOptions
): UseUnifiedCellStyleReturn => {
  const {
    coordinate,
    enableCache = true,
    breakpoint,
    debug = false,
    ...styleOptions
  } = options;

  const styleService = useMemo(() => {
    const service = getStyleService();
    service.setDebugMode(debug);
    return service;
  }, [debug]);

  // 计算样式和类名
  const result = useMemo(() => {
    const calculationOptions: StyleCalculationOptions = {
      coordinate,
      ...styleOptions
    };

    return styleService.calculateStyle(calculationOptions);
  }, [
    coordinate.x,
    coordinate.y,
    styleOptions.cellData?.isSelected,
    styleOptions.cellData?.isHovered,
    styleOptions.cellData?.isFocused,
    styleOptions.cellData?.level,
    styleOptions.cellData?.color,
    styleOptions.renderData?.style,
    styleOptions.renderData?.className,
    styleOptions.config?.mainMode,
    styleOptions.config?.contentMode,
    styleOptions.isEnhanced,
    styleOptions.isWordInputActive,
    styleOptions.customStyle,
    styleOptions.customClassName,
    styleOptions.conditionalClasses,
    styleOptions.useBEM,
    styleOptions.theme,
    styleService
  ]);

  // 应用响应式样式
  const finalStyle = useMemo(() => {
    if (breakpoint) {
      return createResponsiveStyles(result.style, breakpoint);
    }
    return result.style;
  }, [result.style, breakpoint]);

  // 更新选项
  const updateOptions = useCallback((newOptions: Partial<UseUnifiedCellStyleOptions>) => {
    // 这个方法主要用于触发重新渲染
    // 实际的选项更新通过props传递
    if (debug) {
      console.log('[useUnifiedCellStyle] 选项更新:', newOptions);
    }
  }, [debug]);

  // 清除缓存
  const clearCache = useCallback(() => {
    if (enableCache) {
      const cacheKey = `${coordinate.x},${coordinate.y}`;
      styleService.clearCache(cacheKey);
      
      if (debug) {
        console.log('[useUnifiedCellStyle] 缓存已清除:', cacheKey);
      }
    }
  }, [styleService, coordinate, enableCache, debug]);

  // 获取主题样式
  const getThemeStyles = useCallback(() => {
    if (styleOptions.theme) {
      return createThemeStyles(styleOptions.theme);
    }
    return {};
  }, [styleOptions.theme]);

  // 获取响应式样式
  const getResponsiveStyles = useCallback(() => {
    if (breakpoint) {
      return createResponsiveStyles(result.style, breakpoint);
    }
    return result.style;
  }, [result.style, breakpoint]);

  // 重新计算样式
  const recalculate = useCallback(() => {
    clearCache(); // 先清除缓存
    
    const calculationOptions: StyleCalculationOptions = {
      coordinate,
      ...styleOptions
    };

    return styleService.calculateStyle(calculationOptions);
  }, [styleService, coordinate, styleOptions, clearCache]);

  return {
    ...result,
    style: finalStyle,
    updateOptions,
    clearCache,
    getThemeStyles,
    getResponsiveStyles,
    recalculate
  };
};

// ===== 专用Hook =====

/**
 * 简化的单元格样式Hook
 */
export const useSimpleUnifiedCellStyle = (
  x: number,
  y: number,
  isEnhanced: boolean = false,
  customStyle?: React.CSSProperties
) => {
  return useUnifiedCellStyle({
    coordinate: { x, y },
    isEnhanced,
    customStyle,
    enableCache: true
  });
};

/**
 * 类名专用Hook
 */
export const useUnifiedCellClassName = (
  options: UseUnifiedCellStyleOptions
): string => {
  const { className } = useUnifiedCellStyle(options);
  return className;
};

/**
 * 样式专用Hook
 */
export const useUnifiedCellStyleOnly = (
  options: UseUnifiedCellStyleOptions
): React.CSSProperties => {
  const { style } = useUnifiedCellStyle(options);
  return style;
};

/**
 * 主题样式Hook
 */
export const useUnifiedThemeStyle = (theme?: StyleTheme) => {
  return useMemo(() => {
    if (theme) {
      return createThemeStyles(theme);
    }
    return {};
  }, [theme]);
};

/**
 * 响应式单元格样式Hook
 */
export const useResponsiveUnifiedCellStyle = (
  options: UseUnifiedCellStyleOptions,
  breakpoint: 'sm' | 'md' | 'lg' | 'xl'
) => {
  return useUnifiedCellStyle({
    ...options,
    breakpoint
  });
};

/**
 * 批量样式计算Hook
 */
export const useBatchUnifiedCellStyle = (
  optionsList: UseUnifiedCellStyleOptions[]
): StyleCalculationResult[] => {
  const styleService = useMemo(() => getStyleService(), []);

  return useMemo(() => {
    const calculationOptionsList: StyleCalculationOptions[] = optionsList.map(
      ({ coordinate, ...styleOptions }) => ({
        coordinate,
        ...styleOptions
      })
    );

    return styleService.calculateBatchStyles(calculationOptionsList);
  }, [styleService, optionsList]);
};

/**
 * 条件样式Hook
 */
export const useConditionalUnifiedCellStyle = (
  options: UseUnifiedCellStyleOptions,
  condition: boolean
) => {
  const result = useUnifiedCellStyle(options);
  
  return useMemo(() => {
    if (condition) {
      return result;
    }
    
    // 返回基础样式
    return {
      ...result,
      style: {},
      className: 'matrix-cell',
      classArray: ['matrix-cell'],
      modifiers: [],
      stateClasses: [],
      isSpecial: false,
      priority: 0
    };
  }, [result, condition]);
};

/**
 * 动画样式Hook
 */
export const useAnimatedUnifiedCellStyle = (
  options: UseUnifiedCellStyleOptions,
  animationDuration: number = 200
) => {
  const result = useUnifiedCellStyle(options);
  
  const animatedStyle = useMemo(() => ({
    ...result.style,
    transition: `all ${animationDuration}ms ease-in-out`
  }), [result.style, animationDuration]);

  return {
    ...result,
    style: animatedStyle
  };
};

/**
 * 缓存优化Hook
 */
export const useCachedUnifiedCellStyle = (
  options: UseUnifiedCellStyleOptions,
  cacheKey?: string
) => {
  const styleService = useMemo(() => getStyleService(), []);
  
  const result = useUnifiedCellStyle({
    ...options,
    enableCache: true
  });

  // 预热缓存
  useMemo(() => {
    if (cacheKey) {
      const calculationOptions: StyleCalculationOptions = {
        coordinate: options.coordinate,
        ...options
      };
      styleService.preloadCache([calculationOptions]);
    }
  }, [styleService, cacheKey, options]);

  return result;
};

// ===== 工具Hook =====

/**
 * 样式服务Hook
 */
export const useStyleService = () => {
  return useMemo(() => getStyleService(), []);
};

/**
 * 样式缓存统计Hook
 */
export const useStyleCacheStats = () => {
  const styleService = useStyleService();
  
  return useMemo(() => {
    return styleService.getCacheStats();
  }, [styleService]);
};

// ===== 导出类型 =====
export type {
  UseUnifiedCellStyleOptions,
  UseUnifiedCellStyleReturn
};
