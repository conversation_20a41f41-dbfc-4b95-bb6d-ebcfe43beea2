/**
 * 统一滚动管理Hook - 重构版本
 * 🎯 核心价值：整合所有滚动相关Hook，消除重复逻辑，提供一致的滚动体验
 * 📦 功能范围：滚动控制、位置管理、状态跟踪、性能优化
 * 🔄 架构设计：基于统一滚动服务的React Hook封装
 */

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import {
  ScrollManagementService,
  getScrollService,
  scrollToElement,
  type ScrollOptions,
  type ScrollResult,
  type ScrollPosition
} from '@/core/services/ScrollManagementService';

// ===== 类型定义 =====

export interface UseUnifiedScrollManagerOptions extends ScrollOptions {
  /** 容器选择器 */
  containerSelector?: string;
  /** 是否自动初始化容器 */
  autoInitContainer?: boolean;
  /** 位置保存键 */
  positionKey?: string;
  /** 是否启用位置保存 */
  enablePositionSave?: boolean;
  /** 防抖延迟 */
  debounceDelay?: number;
  /** 最大重试次数 */
  maxRetries?: number;
}

export interface UseUnifiedScrollManagerReturn {
  /** 滚动到目标元素 */
  scrollToTarget: (target: HTMLElement, options?: ScrollOptions) => Promise<ScrollResult>;
  /** 滚动到指定位置 */
  scrollToPosition: (position: number, options?: ScrollOptions) => Promise<ScrollResult>;
  /** 计算滚动位置 */
  calculatePosition: (target: HTMLElement) => ScrollPosition | null;
  /** 获取当前滚动位置 */
  getCurrentPosition: () => number;
  /** 保存当前位置 */
  saveCurrentPosition: () => void;
  /** 恢复保存的位置 */
  restorePosition: () => boolean;
  /** 重置滚动状态 */
  resetScrollState: () => void;
  /** 设置滚动容器 */
  setContainer: (container: HTMLElement) => void;
  /** 获取容器信息 */
  getContainerInfo: () => any;
  /** 滚动状态 */
  scrollState: {
    isScrolling: boolean;
    lastScrollTime: number;
    scrollDirection: 'up' | 'down' | 'none';
  };
}

// ===== 主Hook =====

/**
 * 统一滚动管理Hook
 */
export const useUnifiedScrollManager = (
  options: UseUnifiedScrollManagerOptions = {}
): UseUnifiedScrollManagerReturn => {
  const {
    containerSelector,
    autoInitContainer = true,
    positionKey,
    enablePositionSave = false,
    debounceDelay = 150,
    maxRetries = 10,
    behavior = 'smooth',
    block = 'center',
    force = false,
    debug = false
  } = options;

  // 服务实例和容器引用
  const scrollService = useRef(getScrollService());
  const containerRef = useRef<HTMLElement | null>(null);
  const positionKeyRef = useRef(positionKey);
  const initRetryCount = useRef(0);

  // 滚动状态
  const [scrollState, setScrollState] = useState({
    isScrolling: false,
    lastScrollTime: 0,
    scrollDirection: 'none' as 'up' | 'down' | 'none'
  });

  // 防抖定时器
  const debounceTimer = useRef<NodeJS.Timeout>();

  // 设置调试模式
  useEffect(() => {
    scrollService.current.setDebugMode(debug);
  }, [debug]);

  // 自动初始化容器
  useEffect(() => {
    if (!autoInitContainer || containerRef.current) return;

    const tryInitialize = () => {
      let container: HTMLElement | null = null;

      if (containerSelector) {
        container = document.querySelector(containerSelector) as HTMLElement;
      } else {
        // 默认查找常见的滚动容器
        const selectors = [
          '.overflow-y-auto',
          '.overflow-auto',
          '[data-scroll-container]',
          '.scroll-container'
        ];

        for (const selector of selectors) {
          container = document.querySelector(selector) as HTMLElement;
          if (container) break;
        }
      }

      if (container) {
        containerRef.current = container;
        scrollService.current.cacheContainer('default', container);
        
        if (debug) {
          console.log('[useUnifiedScrollManager] 容器初始化成功:', container);
        }
        
        // 如果启用位置保存，尝试恢复位置
        if (enablePositionSave && positionKeyRef.current) {
          setTimeout(() => {
            restorePosition();
          }, 100);
        }
      } else {
        initRetryCount.current++;
        if (initRetryCount.current < maxRetries) {
          if (debug) {
            console.log(`[useUnifiedScrollManager] 容器未找到，重试 ${initRetryCount.current}/${maxRetries}`);
          }
          setTimeout(tryInitialize, 200);
        } else {
          if (debug) {
            console.warn('[useUnifiedScrollManager] 容器初始化失败，已达到最大重试次数');
          }
        }
      }
    };

    const timer = setTimeout(tryInitialize, 100);
    return () => clearTimeout(timer);
  }, [autoInitContainer, containerSelector, maxRetries, debug, enablePositionSave]);

  /**
   * 滚动到目标元素
   */
  const scrollToTarget = useCallback(async (
    target: HTMLElement,
    overrideOptions?: ScrollOptions
  ): Promise<ScrollResult> => {
    const finalOptions: ScrollOptions = {
      behavior,
      block,
      force,
      debug,
      ...overrideOptions
    };

    setScrollState(prev => ({ ...prev, isScrolling: true }));

    try {
      const result = await scrollToElement(target, {
        ...finalOptions,
        containerSelector
      });

      setScrollState(prev => ({
        ...prev,
        isScrolling: false,
        lastScrollTime: Date.now(),
        scrollDirection: result.position.direction
      }));

      // 保存位置（如果启用）
      if (enablePositionSave && positionKeyRef.current && containerRef.current) {
        if (debounceTimer.current) {
          clearTimeout(debounceTimer.current);
        }
        
        debounceTimer.current = setTimeout(() => {
          saveCurrentPosition();
        }, debounceDelay);
      }

      return result;
    } catch (error) {
      setScrollState(prev => ({ ...prev, isScrolling: false }));
      throw error;
    }
  }, [behavior, block, force, debug, containerSelector, enablePositionSave, debounceDelay]);

  /**
   * 滚动到指定位置
   */
  const scrollToPosition = useCallback(async (
    position: number,
    overrideOptions?: ScrollOptions
  ): Promise<ScrollResult> => {
    if (!containerRef.current) {
      return {
        success: false,
        position: { current: 0, target: position, delta: 0, direction: 'none' },
        error: '容器未初始化',
        executionTime: 0
      };
    }

    const finalOptions: ScrollOptions = {
      behavior,
      debug,
      ...overrideOptions
    };

    const current = containerRef.current.scrollTop;
    const delta = position - current;
    const direction = delta === 0 ? 'none' : delta < 0 ? 'up' : 'down';

    const scrollPosition: ScrollPosition = {
      current,
      target: position,
      delta,
      direction
    };

    setScrollState(prev => ({ ...prev, isScrolling: true }));

    try {
      const result = await ScrollManagementService.executeScroll(
        containerRef.current,
        scrollPosition,
        finalOptions
      );

      setScrollState(prev => ({
        ...prev,
        isScrolling: false,
        lastScrollTime: Date.now(),
        scrollDirection: direction
      }));

      return result;
    } catch (error) {
      setScrollState(prev => ({ ...prev, isScrolling: false }));
      throw error;
    }
  }, [behavior, debug]);

  /**
   * 计算滚动位置
   */
  const calculatePosition = useCallback((target: HTMLElement): ScrollPosition | null => {
    if (!containerRef.current) return null;

    return ScrollManagementService.calculateScrollPosition(
      target,
      containerRef.current,
      block
    );
  }, [block]);

  /**
   * 获取当前滚动位置
   */
  const getCurrentPosition = useCallback((): number => {
    return containerRef.current?.scrollTop || 0;
  }, []);

  /**
   * 保存当前位置
   */
  const saveCurrentPosition = useCallback((): void => {
    if (enablePositionSave && positionKeyRef.current && containerRef.current) {
      scrollService.current.saveScrollPosition(positionKeyRef.current, containerRef.current);
    }
  }, [enablePositionSave]);

  /**
   * 恢复保存的位置
   */
  const restorePosition = useCallback((): boolean => {
    if (enablePositionSave && positionKeyRef.current && containerRef.current) {
      return scrollService.current.restoreScrollPosition(positionKeyRef.current, containerRef.current);
    }
    return false;
  }, [enablePositionSave]);

  /**
   * 重置滚动状态
   */
  const resetScrollState = useCallback((): void => {
    scrollService.current.clearPositionHistory();
    scrollService.current.clearContainerCache();
    setScrollState({
      isScrolling: false,
      lastScrollTime: 0,
      scrollDirection: 'none'
    });
  }, []);

  /**
   * 设置滚动容器
   */
  const setContainer = useCallback((container: HTMLElement): void => {
    containerRef.current = container;
    scrollService.current.cacheContainer('default', container);
    
    if (debug) {
      console.log('[useUnifiedScrollManager] 手动设置容器:', container);
    }
  }, [debug]);

  /**
   * 获取容器信息
   */
  const getContainerInfo = useCallback(() => {
    if (!containerRef.current) return null;
    return ScrollManagementService.getContainerInfo(containerRef.current);
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  return {
    scrollToTarget,
    scrollToPosition,
    calculatePosition,
    getCurrentPosition,
    saveCurrentPosition,
    restorePosition,
    resetScrollState,
    setContainer,
    getContainerInfo,
    scrollState
  };
};

// ===== 专用Hook =====

/**
 * 词库滚动管理Hook
 */
export const useWordLibraryScrollManager = (debug = false) => {
  return useUnifiedScrollManager({
    containerSelector: '.word-library-manager .overflow-y-auto',
    positionKey: 'word-library-scroll',
    enablePositionSave: true,
    debounceDelay: 150,
    debug
  });
};

/**
 * 矩阵滚动管理Hook
 */
export const useMatrixScrollManager = (debug = false) => {
  return useUnifiedScrollManager({
    containerSelector: '.matrix-container',
    positionKey: 'matrix-scroll',
    enablePositionSave: false,
    debug
  });
};

// ===== 导出类型 =====
export type {
  UseUnifiedScrollManagerOptions,
  UseUnifiedScrollManagerReturn
};
