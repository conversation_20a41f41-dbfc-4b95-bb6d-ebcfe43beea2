/**
 * 优化的词库Store Hook
 * 🎯 核心价值：使用选择器模式避免不必要的组件重新渲染
 * 📦 功能范围：精确订阅词库状态，防止过度渲染
 * 🔄 架构设计：基于选择器的状态订阅，最小化重新渲染
 */

'use client';

import type { WordLibraryKey } from '@/core/matrix/MatrixTypes';
import {
  selectActiveLibrary,
  selectDuplicateWords,
  selectIsLibraryActive,
  selectLibrary,
  selectLibraryCollapsed,
  selectLibraryWordCount,
  selectLibraryWords,
  selectMatchedLibrary,
  selectSelectedCell,
  selectTemporaryWord,
  selectWordInputActive,
  useWordInputStore,
  useWordLibraryStore
} from '@/core/wordLibrary/WordLibraryStore';
import { useMemo } from 'react';

// ===== 类型定义 =====

interface UseOptimizedLibraryReturn {
  library: ReturnType<typeof selectLibrary>;
  collapsed: boolean;
  words: ReturnType<typeof selectLibraryWords>;
  wordCount: number;
}

interface UseOptimizedWordInputReturn {
  isActive: boolean;
  matchedLibrary: string | null;
  temporaryWord: string | null;
  selectedCell: ReturnType<typeof selectSelectedCell>;
  isLibraryActive: boolean;
}

// ===== 优化的词库Hook =====

/**
 * 使用优化的词库状态订阅
 * 只订阅特定词库的状态，避免其他词库变化导致重新渲染
 */
export const useOptimizedLibrary = (libraryKey: WordLibraryKey): UseOptimizedLibraryReturn => {
  // 使用选择器精确订阅
  const library = useWordLibraryStore(selectLibrary(libraryKey));
  const collapsed = useWordLibraryStore(selectLibraryCollapsed(libraryKey));
  const words = useWordLibraryStore(selectLibraryWords(libraryKey));
  const wordCount = useWordLibraryStore(selectLibraryWordCount(libraryKey));

  return useMemo(() => ({
    library,
    collapsed,
    words,
    wordCount
  }), [library, collapsed, words, wordCount]);
};

/**
 * 使用优化的填词模式状态订阅
 * 只订阅填词相关状态，避免词库内容变化导致重新渲染
 */
export const useOptimizedWordInput = (libraryKey?: WordLibraryKey): UseOptimizedWordInputReturn => {
  // 使用选择器精确订阅
  const isActive = useWordInputStore(selectWordInputActive);
  const matchedLibrary = useWordInputStore(selectMatchedLibrary);
  const temporaryWord = useWordInputStore(selectTemporaryWord);
  const selectedCell = useWordInputStore(selectSelectedCell);

  // 计算当前词库是否激活
  const isLibraryActive = useMemo(() => {
    if (!libraryKey) return false;
    return isActive && matchedLibrary === libraryKey;
  }, [isActive, matchedLibrary, libraryKey]);

  return useMemo(() => ({
    isActive,
    matchedLibrary,
    temporaryWord,
    selectedCell,
    isLibraryActive
  }), [isActive, matchedLibrary, temporaryWord, selectedCell, isLibraryActive]);
};

/**
 * 使用词库操作方法
 * 分离操作方法，避免状态变化影响
 */
export const useWordLibraryActions = () => {
  const store = useWordLibraryStore();

  return useMemo(() => ({
    addWord: store.addWord,
    removeWord: store.removeWord,
    updateWord: store.updateWord,
    clearLibrary: store.clearLibrary,
    toggleLibraryCollapse: store.toggleLibraryCollapse,
    setActiveLibrary: store.setActiveLibrary,
    validateInput: store.validateInput,
    checkCrossLibraryDuplicate: store.checkCrossLibraryDuplicate,
    getWordHighlightColor: store.getWordHighlightColor,
    setWordHighlightColor: store.setWordHighlightColor,
    resetAllLibraries: store.resetAllLibraries,
    exportData: store.exportData,
    importData: store.importData
  }), [
    store.addWord,
    store.removeWord,
    store.updateWord,
    store.clearLibrary,
    store.toggleLibraryCollapse,
    store.setActiveLibrary,
    store.validateInput,
    store.checkCrossLibraryDuplicate,
    store.getWordHighlightColor,
    store.setWordHighlightColor,
    store.resetAllLibraries,
    store.exportData,
    store.importData
  ]);
};

/**
 * 使用填词操作方法
 * 分离操作方法，避免状态变化影响
 */
export const useWordInputActions = () => {
  const store = useWordInputStore();

  return useMemo(() => ({
    activateWordInput: store.activateWordInput,
    deactivateWordInput: store.deactivateWordInput,
    selectNextWord: store.selectNextWord,
    selectPreviousWord: store.selectPreviousWord,
    confirmWordSelection: store.confirmWordSelection,
    clearCellWord: store.clearCellWord,
    checkLibraryEmpty: store.checkLibraryEmpty
  }), [
    store.activateWordInput,
    store.deactivateWordInput,
    store.selectNextWord,
    store.selectPreviousWord,
    store.confirmWordSelection,
    store.clearCellWord,
    store.checkLibraryEmpty
  ]);
};

/**
 * 使用全局词库状态
 * 用于需要访问全局状态的组件
 */
export const useGlobalWordLibraryState = () => {
  const activeLibrary = useWordLibraryStore(selectActiveLibrary);
  const duplicateWords = useWordLibraryStore(selectDuplicateWords);

  return useMemo(() => ({
    activeLibrary,
    duplicateWords
  }), [activeLibrary, duplicateWords]);
};

// ===== 便捷Hook =====

/**
 * 检查词库是否激活的便捷Hook
 */
export const useIsLibraryActive = (libraryKey: WordLibraryKey): boolean => {
  return useWordInputStore(selectIsLibraryActive(libraryKey));
};

/**
 * 获取当前临时词语的便捷Hook
 */
export const useCurrentTemporaryWord = (): string | null => {
  return useWordInputStore(selectTemporaryWord);
};

/**
 * 获取当前匹配词库的便捷Hook
 */
export const useCurrentMatchedLibrary = (): string | null => {
  return useWordInputStore(selectMatchedLibrary);
};

// ===== 导出类型 =====

export type {
  UseOptimizedLibraryReturn,
  UseOptimizedWordInputReturn
};

