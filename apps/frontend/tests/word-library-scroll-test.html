<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词库滚动稳定性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-content {
            display: flex;
            height: 600px;
        }
        
        .test-controls {
            width: 300px;
            padding: 20px;
            border-right: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        
        .test-preview {
            flex: 1;
            padding: 20px;
            overflow: hidden;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            margin-bottom: 8px;
            border: none;
            border-radius: 6px;
            background: #3b82f6;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .control-group button:hover {
            background: #2563eb;
        }
        
        .control-group button.danger {
            background: #ef4444;
        }
        
        .control-group button.danger:hover {
            background: #dc2626;
        }
        
        .control-group button.success {
            background: #10b981;
        }
        
        .control-group button.success:hover {
            background: #059669;
        }
        
        .scroll-indicator {
            background: #1f2937;
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 10px;
        }
        
        .test-status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .test-status.idle {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .test-status.running {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .test-status.passed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .test-status.failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .mock-word-library {
            height: 500px;
            overflow-y: auto;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .mock-library-item {
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s;
        }
        
        .mock-library-item:hover {
            background: #f9fafb;
        }
        
        .mock-library-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .mock-library-title {
            font-weight: 600;
            color: #1f2937;
        }
        
        .mock-word-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .mock-word-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .mock-word-tag {
            background: #eff6ff;
            color: #1e40af;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .mock-word-tag:hover {
            background: #dbeafe;
        }
        
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
            color: #78350f;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>词库滚动稳定性测试</h1>
            <p>验证重构后的词库管理组件在非滑动交互时不会触发滚动位置重置</p>
        </div>
        
        <div class="test-content">
            <div class="test-controls">
                <div class="instructions">
                    <h3>测试说明</h3>
                    <ul>
                        <li>滚动右侧词库列表到任意位置</li>
                        <li>执行各种操作（添加、删除词语等）</li>
                        <li>观察滚动位置是否保持稳定</li>
                        <li>绿色表示测试通过，红色表示失败</li>
                    </ul>
                </div>
                
                <div class="control-group">
                    <label>滚动位置监控</label>
                    <div class="scroll-indicator" id="scrollIndicator">
                        滚动位置: 0px
                    </div>
                </div>
                
                <div class="control-group">
                    <label>测试状态</label>
                    <div class="test-status idle" id="testStatus">
                        等待测试开始
                    </div>
                </div>
                
                <div class="control-group">
                    <label>手动测试操作</label>
                    <button onclick="testAddWord()">测试添加词语</button>
                    <button onclick="testRemoveWord()" class="danger">测试删除词语</button>
                    <button onclick="testToggleCollapse()">测试折叠/展开</button>
                    <button onclick="testScrollPosition()" class="success">测试滚动恢复</button>
                </div>
                
                <div class="control-group">
                    <label>自动化测试</label>
                    <button onclick="runAutomatedTest()" class="success">运行完整测试</button>
                    <button onclick="resetTest()">重置测试</button>
                </div>
                
                <div class="control-group">
                    <label>滚动控制</label>
                    <button onclick="scrollToPosition(0)">滚动到顶部</button>
                    <button onclick="scrollToPosition(200)">滚动到200px</button>
                    <button onclick="scrollToPosition(500)">滚动到500px</button>
                </div>
            </div>
            
            <div class="test-preview">
                <div class="mock-word-library" id="mockWordLibrary">
                    <!-- 模拟词库内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试状态
        let currentScrollPosition = 0;
        let testResults = [];
        let isTestRunning = false;

        // 初始化模拟词库
        function initMockWordLibrary() {
            const container = document.getElementById('mockWordLibrary');
            const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan'];
            const levels = [1, 2, 3, 4];
            
            colors.forEach(color => {
                levels.forEach(level => {
                    const item = document.createElement('div');
                    item.className = 'mock-library-item';
                    item.innerHTML = `
                        <div class="mock-library-header">
                            <span class="mock-library-title">${color}-${level} 词库</span>
                        </div>
                        <input type="text" class="mock-word-input" placeholder="输入词语..." 
                               onkeydown="handleWordInput(event, '${color}-${level}')">
                        <div class="mock-word-list" id="words-${color}-${level}">
                            <span class="mock-word-tag" onclick="removeWord(this)">示例词语1</span>
                            <span class="mock-word-tag" onclick="removeWord(this)">示例词语2</span>
                        </div>
                    `;
                    container.appendChild(item);
                });
            });
        }

        // 监控滚动位置
        function monitorScrollPosition() {
            const container = document.getElementById('mockWordLibrary');
            const indicator = document.getElementById('scrollIndicator');
            
            container.addEventListener('scroll', () => {
                currentScrollPosition = container.scrollTop;
                indicator.textContent = `滚动位置: ${currentScrollPosition}px`;
            });
        }

        // 更新测试状态
        function updateTestStatus(message, type = 'idle') {
            const status = document.getElementById('testStatus');
            status.textContent = message;
            status.className = `test-status ${type}`;
        }

        // 滚动到指定位置
        function scrollToPosition(position) {
            const container = document.getElementById('mockWordLibrary');
            container.scrollTop = position;
            updateTestStatus(`已滚动到 ${position}px`, 'running');
        }

        // 检查滚动位置稳定性
        function checkScrollStability(expectedPosition, testName) {
            const container = document.getElementById('mockWordLibrary');
            const actualPosition = container.scrollTop;
            const delta = Math.abs(actualPosition - expectedPosition);
            const isStable = delta <= 5; // 5px容差
            
            const result = {
                testName,
                expectedPosition,
                actualPosition,
                delta,
                isStable,
                timestamp: new Date().toISOString()
            };
            
            testResults.push(result);
            
            updateTestStatus(
                `${testName}: ${isStable ? '通过' : '失败'} (差值: ${delta}px)`,
                isStable ? 'passed' : 'failed'
            );
            
            return isStable;
        }

        // 处理词语输入
        function handleWordInput(event, libraryKey) {
            if (event.key === 'Enter') {
                const input = event.target;
                const wordText = input.value.trim();
                
                if (wordText) {
                    const wordList = document.getElementById(`words-${libraryKey}`);
                    const wordTag = document.createElement('span');
                    wordTag.className = 'mock-word-tag';
                    wordTag.textContent = wordText;
                    wordTag.onclick = () => removeWord(wordTag);
                    wordList.appendChild(wordTag);
                    
                    input.value = '';
                    
                    // 检查滚动稳定性
                    setTimeout(() => {
                        checkScrollStability(currentScrollPosition, '添加词语操作');
                    }, 100);
                }
            }
        }

        // 删除词语
        function removeWord(element) {
            const savedPosition = currentScrollPosition;
            element.remove();
            
            // 检查滚动稳定性
            setTimeout(() => {
                checkScrollStability(savedPosition, '删除词语操作');
            }, 100);
        }

        // 测试函数
        function testAddWord() {
            const savedPosition = currentScrollPosition;
            updateTestStatus('正在测试添加词语...', 'running');
            
            // 模拟添加词语
            const firstInput = document.querySelector('.mock-word-input');
            if (firstInput) {
                firstInput.value = '测试词语' + Date.now();
                firstInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter' }));
            }
        }

        function testRemoveWord() {
            const savedPosition = currentScrollPosition;
            updateTestStatus('正在测试删除词语...', 'running');
            
            // 模拟删除词语
            const firstWord = document.querySelector('.mock-word-tag');
            if (firstWord) {
                removeWord(firstWord);
            } else {
                updateTestStatus('没有可删除的词语', 'failed');
            }
        }

        function testToggleCollapse() {
            const savedPosition = currentScrollPosition;
            updateTestStatus('正在测试折叠/展开...', 'running');
            
            // 模拟折叠操作（简化版）
            setTimeout(() => {
                checkScrollStability(savedPosition, '折叠/展开操作');
            }, 100);
        }

        function testScrollPosition() {
            updateTestStatus('正在测试滚动恢复...', 'running');
            
            const testPosition = 300;
            scrollToPosition(testPosition);
            
            // 模拟组件重新渲染
            setTimeout(() => {
                checkScrollStability(testPosition, '滚动位置恢复');
            }, 200);
        }

        function runAutomatedTest() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            testResults = [];
            updateTestStatus('正在运行自动化测试...', 'running');
            
            const tests = [
                () => { scrollToPosition(150); return new Promise(resolve => setTimeout(resolve, 500)); },
                () => { testAddWord(); return new Promise(resolve => setTimeout(resolve, 500)); },
                () => { scrollToPosition(250); return new Promise(resolve => setTimeout(resolve, 500)); },
                () => { testRemoveWord(); return new Promise(resolve => setTimeout(resolve, 500)); },
                () => { scrollToPosition(350); return new Promise(resolve => setTimeout(resolve, 500)); },
                () => { testToggleCollapse(); return new Promise(resolve => setTimeout(resolve, 500)); },
                () => { testScrollPosition(); return new Promise(resolve => setTimeout(resolve, 500)); }
            ];
            
            // 顺序执行测试
            tests.reduce((promise, test) => {
                return promise.then(test);
            }, Promise.resolve()).then(() => {
                isTestRunning = false;
                
                const passedTests = testResults.filter(r => r.isStable).length;
                const totalTests = testResults.length;
                
                updateTestStatus(
                    `测试完成: ${passedTests}/${totalTests} 通过`,
                    passedTests === totalTests ? 'passed' : 'failed'
                );
                
                console.log('测试结果:', testResults);
            });
        }

        function resetTest() {
            testResults = [];
            isTestRunning = false;
            scrollToPosition(0);
            updateTestStatus('测试已重置', 'idle');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initMockWordLibrary();
            monitorScrollPosition();
            updateTestStatus('测试环境已就绪', 'idle');
        });
    </script>
</body>
</html>
