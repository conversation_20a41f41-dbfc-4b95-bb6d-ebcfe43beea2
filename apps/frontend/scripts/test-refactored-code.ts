/**
 * 重构代码测试脚本
 * 🎯 核心价值：验证重构后的代码功能正确性和性能提升
 * 📦 功能范围：滚动服务测试、验证服务测试、状态更新测试
 * 🔄 架构设计：自动化测试脚本，支持性能对比和功能验证
 */

import { ScrollManagementService, getScrollService } from '../core/services/ScrollManagementService';
import { UnifiedWordValidationService, getValidationService } from '../core/wordLibrary/WordValidationService';
import { createStateUpdater, createBatchStateUpdater } from '../core/utils/StateUpdateUtils';

// ===== 测试配置 =====

interface TestConfig {
  enablePerformanceTest: boolean;
  enableFunctionalTest: boolean;
  enableIntegrationTest: boolean;
  verbose: boolean;
}

const DEFAULT_TEST_CONFIG: TestConfig = {
  enablePerformanceTest: true,
  enableFunctionalTest: true,
  enableIntegrationTest: true,
  verbose: true
};

// ===== 测试工具 =====

class TestRunner {
  private config: TestConfig;
  private results: Map<string, any> = new Map();

  constructor(config: TestConfig = DEFAULT_TEST_CONFIG) {
    this.config = config;
  }

  log(message: string, data?: any): void {
    if (this.config.verbose) {
      console.log(`[测试] ${message}`, data || '');
    }
  }

  error(message: string, error?: any): void {
    console.error(`[测试错误] ${message}`, error || '');
  }

  async runTest(name: string, testFn: () => Promise<any>): Promise<boolean> {
    try {
      this.log(`开始测试: ${name}`);
      const startTime = performance.now();
      
      const result = await testFn();
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.results.set(name, {
        success: true,
        result,
        duration,
        timestamp: new Date().toISOString()
      });
      
      this.log(`测试完成: ${name} (${duration.toFixed(2)}ms)`, result);
      return true;
    } catch (error) {
      this.error(`测试失败: ${name}`, error);
      this.results.set(name, {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      });
      return false;
    }
  }

  getResults(): Map<string, any> {
    return this.results;
  }

  printSummary(): void {
    const total = this.results.size;
    const passed = Array.from(this.results.values()).filter(r => r.success).length;
    const failed = total - passed;

    console.log('\n=== 测试总结 ===');
    console.log(`总计: ${total} 个测试`);
    console.log(`通过: ${passed} 个测试`);
    console.log(`失败: ${failed} 个测试`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n失败的测试:');
      for (const [name, result] of this.results.entries()) {
        if (!result.success) {
          console.log(`- ${name}: ${result.error}`);
        }
      }
    }
  }
}

// ===== 滚动服务测试 =====

async function testScrollManagementService(): Promise<any> {
  const service = getScrollService();
  
  // 测试服务实例
  if (!service) {
    throw new Error('滚动服务实例获取失败');
  }

  // 测试静态方法
  const mockElement = document.createElement('div');
  const mockContainer = document.createElement('div');
  
  // 设置模拟DOM结构
  mockContainer.style.overflowY = 'auto';
  mockContainer.style.height = '500px';
  mockElement.style.height = '100px';
  mockContainer.appendChild(mockElement);
  document.body.appendChild(mockContainer);

  try {
    // 测试容器查找
    const foundContainer = ScrollManagementService.findScrollContainer(mockElement);
    if (!foundContainer) {
      throw new Error('容器查找失败');
    }

    // 测试位置计算
    const position = ScrollManagementService.calculateScrollPosition(
      mockElement,
      mockContainer,
      'center'
    );
    
    if (typeof position.current !== 'number' || typeof position.target !== 'number') {
      throw new Error('位置计算结果无效');
    }

    // 测试滚动执行
    const scrollResult = await ScrollManagementService.executeScroll(
      mockContainer,
      position,
      { behavior: 'auto', debug: false }
    );

    if (typeof scrollResult.success !== 'boolean') {
      throw new Error('滚动执行结果无效');
    }

    return {
      containerFound: !!foundContainer,
      positionCalculated: true,
      scrollExecuted: scrollResult.success,
      executionTime: scrollResult.executionTime
    };
  } finally {
    // 清理DOM
    document.body.removeChild(mockContainer);
  }
}

// ===== 验证服务测试 =====

async function testWordValidationService(): Promise<any> {
  const service = getValidationService();
  
  if (!service) {
    throw new Error('验证服务实例获取失败');
  }

  // 测试格式验证
  const formatTests = [
    { text: '测试词语', expected: true },
    { text: '', expected: false },
    { text: '   ', expected: false },
    { text: 'a'.repeat(100), expected: false }, // 超长
    { text: '测试@词语', expected: false }, // 特殊字符
  ];

  const formatResults = formatTests.map(test => {
    const result = service.validateFormat(test.text);
    return {
      text: test.text,
      expected: test.expected,
      actual: result.isValid,
      passed: result.isValid === test.expected
    };
  });

  const formatPassed = formatResults.every(r => r.passed);

  // 测试重复检测
  const mockLibraries = new Map();
  mockLibraries.set('test-1', {
    words: [{ text: '重复词语', id: '1' }]
  });

  const duplicateResult = service.checkDuplicates('重复词语', {
    libraryKey: 'test-2',
    libraries: mockLibraries
  });

  const duplicateDetected = duplicateResult.isDuplicate && 
    duplicateResult.duplicateLibraries.includes('test-1');

  // 测试完整验证
  const completeResult = service.validateComplete('新词语', {
    libraryKey: 'test-1',
    libraries: mockLibraries
  });

  const completeValidation = typeof completeResult.isValid === 'boolean';

  return {
    formatValidation: formatPassed,
    duplicateDetection: duplicateDetected,
    completeValidation,
    formatResults,
    duplicateResult,
    completeResult
  };
}

// ===== 状态更新测试 =====

async function testStateUpdateUtils(): Promise<any> {
  // 模拟状态对象
  interface TestState {
    count: number;
    name: string;
    items: string[];
    config: { enabled: boolean };
    lastUpdate?: number;
    isDirty?: boolean;
  }

  const initialState: TestState = {
    count: 0,
    name: 'test',
    items: [],
    config: { enabled: false }
  };

  // 测试单个更新器
  const singleUpdater = createStateUpdater<TestState>((state) => {
    state.count += 1;
    state.name = 'updated';
  });

  const updatedState1 = singleUpdater(initialState);
  
  const singleUpdatePassed = 
    updatedState1.count === 1 && 
    updatedState1.name === 'updated' &&
    typeof updatedState1.lastUpdate === 'number' &&
    updatedState1.isDirty === true;

  // 测试批量更新器
  const batchUpdater = createBatchStateUpdater<TestState>([
    (state) => { state.count += 5; },
    (state) => { state.items.push('item1', 'item2'); },
    (state) => { state.config.enabled = true; }
  ]);

  const updatedState2 = batchUpdater(initialState);
  
  const batchUpdatePassed = 
    updatedState2.count === 5 &&
    updatedState2.items.length === 2 &&
    updatedState2.config.enabled === true &&
    typeof updatedState2.lastUpdate === 'number';

  return {
    singleUpdate: singleUpdatePassed,
    batchUpdate: batchUpdatePassed,
    singleResult: updatedState1,
    batchResult: updatedState2
  };
}

// ===== 性能对比测试 =====

async function testPerformanceComparison(): Promise<any> {
  const iterations = 1000;
  
  // 测试验证服务性能
  const service = getValidationService();
  const testWords = Array.from({ length: 100 }, (_, i) => `测试词语${i}`);
  
  // 单次验证性能
  const singleValidationStart = performance.now();
  for (let i = 0; i < iterations; i++) {
    service.validateFormat(testWords[i % testWords.length]);
  }
  const singleValidationTime = performance.now() - singleValidationStart;

  // 批量验证性能
  const batchValidationStart = performance.now();
  service.validateBatch(testWords, {});
  const batchValidationTime = performance.now() - batchValidationStart;

  // 状态更新性能
  const stateUpdateStart = performance.now();
  for (let i = 0; i < iterations; i++) {
    const updater = createStateUpdater<any>((state) => {
      state.value = i;
    });
    updater({ value: 0 });
  }
  const stateUpdateTime = performance.now() - stateUpdateStart;

  return {
    singleValidationTime,
    batchValidationTime,
    stateUpdateTime,
    iterations,
    avgSingleValidation: singleValidationTime / iterations,
    avgStateUpdate: stateUpdateTime / iterations
  };
}

// ===== 主测试函数 =====

export async function runRefactoredCodeTests(config?: Partial<TestConfig>): Promise<void> {
  const testConfig = { ...DEFAULT_TEST_CONFIG, ...config };
  const runner = new TestRunner(testConfig);

  console.log('🚀 开始重构代码测试...\n');

  if (testConfig.enableFunctionalTest) {
    await runner.runTest('滚动管理服务', testScrollManagementService);
    await runner.runTest('词语验证服务', testWordValidationService);
    await runner.runTest('状态更新工具', testStateUpdateUtils);
  }

  if (testConfig.enablePerformanceTest) {
    await runner.runTest('性能对比测试', testPerformanceComparison);
  }

  runner.printSummary();

  // 输出详细结果
  if (testConfig.verbose) {
    console.log('\n=== 详细测试结果 ===');
    for (const [name, result] of runner.getResults().entries()) {
      console.log(`\n${name}:`, result);
    }
  }
}

// ===== 导出 =====

export { TestRunner, testScrollManagementService, testWordValidationService, testStateUpdateUtils };

// 如果直接运行此脚本
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runRefactoredCodeTests = runRefactoredCodeTests;
  console.log('测试函数已挂载到 window.runRefactoredCodeTests');
}
