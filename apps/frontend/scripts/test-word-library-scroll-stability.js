/**
 * 词库滚动稳定性测试脚本
 * 🎯 核心价值：验证重构后的词库管理组件不会在非滑动交互时触发滚动位置重置
 * 📦 功能范围：自动化测试词库操作对滚动位置的影响
 * 🔄 架构设计：基于浏览器环境的集成测试
 */

// ===== 测试配置 =====

const TEST_CONFIG = {
  // 测试超时时间
  timeout: 30000,
  // 滚动位置检查间隔
  scrollCheckInterval: 100,
  // 操作间隔时间
  operationDelay: 500,
  // 滚动位置容差
  scrollTolerance: 5,
  // 调试模式
  debug: true
};

// ===== 测试工具函数 =====

class WordLibraryScrollTest {
  constructor() {
    this.testResults = [];
    this.currentScrollPosition = 0;
    this.initialScrollPosition = 0;
    this.testStartTime = Date.now();
  }

  /**
   * 记录测试日志
   */
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    
    if (TEST_CONFIG.debug) {
      console.log(logMessage);
    }
    
    // 在页面上显示日志
    this.displayLog(logMessage, type);
  }

  /**
   * 在页面上显示日志
   */
  displayLog(message, type) {
    let logContainer = document.getElementById('test-log-container');
    if (!logContainer) {
      logContainer = document.createElement('div');
      logContainer.id = 'test-log-container';
      logContainer.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        width: 400px;
        max-height: 500px;
        overflow-y: auto;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 10px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        border: 2px solid #333;
      `;
      document.body.appendChild(logContainer);
    }

    const logEntry = document.createElement('div');
    logEntry.style.cssText = `
      margin-bottom: 5px;
      padding: 3px 6px;
      border-radius: 3px;
      background: ${type === 'error' ? '#ff4444' : type === 'warn' ? '#ffaa00' : type === 'success' ? '#44ff44' : '#4444ff'};
      color: ${type === 'success' ? '#000' : '#fff'};
    `;
    logEntry.textContent = message;
    logContainer.appendChild(logEntry);
    
    // 自动滚动到底部
    logContainer.scrollTop = logContainer.scrollHeight;
  }

  /**
   * 获取词库滚动容器
   */
  getScrollContainer() {
    return document.querySelector('.word-library-manager .overflow-y-auto');
  }

  /**
   * 获取当前滚动位置
   */
  getCurrentScrollPosition() {
    const container = this.getScrollContainer();
    return container ? container.scrollTop : 0;
  }

  /**
   * 设置滚动位置
   */
  setScrollPosition(position) {
    const container = this.getScrollContainer();
    if (container) {
      container.scrollTop = position;
      this.currentScrollPosition = position;
    }
  }

  /**
   * 等待指定时间
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 检查滚动位置是否稳定
   */
  checkScrollStability(expectedPosition, testName) {
    const currentPosition = this.getCurrentScrollPosition();
    const delta = Math.abs(currentPosition - expectedPosition);
    const isStable = delta <= TEST_CONFIG.scrollTolerance;
    
    this.log(`${testName}: 期望位置=${expectedPosition}, 当前位置=${currentPosition}, 差值=${delta}, 稳定=${isStable}`, 
             isStable ? 'success' : 'error');
    
    return isStable;
  }

  /**
   * 测试添加词语操作
   */
  async testAddWordOperation() {
    this.log('开始测试：添加词语操作对滚动位置的影响');
    
    // 设置初始滚动位置
    const testPosition = 200;
    this.setScrollPosition(testPosition);
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 查找第一个词库输入框
    const inputElement = document.querySelector('.word-library-manager input[type="text"]');
    if (!inputElement) {
      this.log('未找到词库输入框', 'error');
      return false;
    }
    
    // 模拟添加词语
    inputElement.focus();
    inputElement.value = '测试词语';
    inputElement.dispatchEvent(new Event('input', { bubbles: true }));
    
    // 模拟回车确认
    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter', bubbles: true });
    inputElement.dispatchEvent(enterEvent);
    
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 检查滚动位置是否稳定
    return this.checkScrollStability(testPosition, '添加词语测试');
  }

  /**
   * 测试删除词语操作
   */
  async testRemoveWordOperation() {
    this.log('开始测试：删除词语操作对滚动位置的影响');
    
    // 设置初始滚动位置
    const testPosition = 300;
    this.setScrollPosition(testPosition);
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 查找删除按钮
    const removeButton = document.querySelector('.word-library-manager .word-tag button');
    if (!removeButton) {
      this.log('未找到删除按钮', 'warn');
      return true; // 如果没有词语可删除，认为测试通过
    }
    
    // 模拟点击删除
    removeButton.click();
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 检查滚动位置是否稳定
    return this.checkScrollStability(testPosition, '删除词语测试');
  }

  /**
   * 测试折叠/展开操作
   */
  async testToggleCollapseOperation() {
    this.log('开始测试：折叠/展开操作对滚动位置的影响');
    
    // 设置初始滚动位置
    const testPosition = 150;
    this.setScrollPosition(testPosition);
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 查找折叠按钮
    const toggleButton = document.querySelector('.word-library-manager .toggle-button');
    if (!toggleButton) {
      this.log('未找到折叠按钮', 'warn');
      return true; // 如果没有折叠按钮，认为测试通过
    }
    
    // 模拟点击折叠
    toggleButton.click();
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 检查滚动位置是否稳定
    const result1 = this.checkScrollStability(testPosition, '折叠操作测试');
    
    // 再次点击展开
    toggleButton.click();
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 检查滚动位置是否稳定
    const result2 = this.checkScrollStability(testPosition, '展开操作测试');
    
    return result1 && result2;
  }

  /**
   * 测试滚动恢复功能
   */
  async testScrollRestoration() {
    this.log('开始测试：滚动位置恢复功能');
    
    // 设置初始滚动位置
    const testPosition = 400;
    this.setScrollPosition(testPosition);
    await this.wait(TEST_CONFIG.operationDelay);
    
    // 模拟组件重新渲染（通过触发状态变化）
    const container = this.getScrollContainer();
    if (container) {
      // 临时隐藏容器
      container.style.display = 'none';
      await this.wait(100);
      
      // 重新显示容器
      container.style.display = '';
      await this.wait(TEST_CONFIG.operationDelay);
      
      // 检查滚动位置是否恢复
      return this.checkScrollStability(testPosition, '滚动恢复测试');
    }
    
    return false;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    this.log('开始词库滚动稳定性测试', 'info');
    
    const tests = [
      { name: '添加词语操作', fn: () => this.testAddWordOperation() },
      { name: '删除词语操作', fn: () => this.testRemoveWordOperation() },
      { name: '折叠/展开操作', fn: () => this.testToggleCollapseOperation() },
      { name: '滚动恢复功能', fn: () => this.testScrollRestoration() }
    ];
    
    const results = [];
    
    for (const test of tests) {
      try {
        this.log(`执行测试：${test.name}`);
        const result = await test.fn();
        results.push({ name: test.name, passed: result });
        this.log(`测试 "${test.name}" ${result ? '通过' : '失败'}`, result ? 'success' : 'error');
      } catch (error) {
        this.log(`测试 "${test.name}" 出错: ${error.message}`, 'error');
        results.push({ name: test.name, passed: false, error: error.message });
      }
      
      // 测试间隔
      await this.wait(1000);
    }
    
    // 生成测试报告
    this.generateTestReport(results);
    
    return results;
  }

  /**
   * 生成测试报告
   */
  generateTestReport(results) {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    this.log('=== 测试报告 ===', 'info');
    this.log(`总测试数: ${totalTests}`, 'info');
    this.log(`通过: ${passedTests}`, 'success');
    this.log(`失败: ${failedTests}`, failedTests > 0 ? 'error' : 'success');
    this.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 'info');
    
    results.forEach(result => {
      this.log(`- ${result.name}: ${result.passed ? '✓' : '✗'}${result.error ? ` (${result.error})` : ''}`, 
               result.passed ? 'success' : 'error');
    });
    
    const testDuration = Date.now() - this.testStartTime;
    this.log(`测试耗时: ${testDuration}ms`, 'info');
  }
}

// ===== 导出测试类 =====

window.WordLibraryScrollTest = WordLibraryScrollTest;

// ===== 自动运行测试 =====

if (typeof window !== 'undefined') {
  // 等待页面加载完成后自动运行测试
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const test = new WordLibraryScrollTest();
        test.runAllTests();
      }, 2000); // 等待2秒确保组件完全加载
    });
  } else {
    setTimeout(() => {
      const test = new WordLibraryScrollTest();
      test.runAllTests();
    }, 2000);
  }
}

console.log('词库滚动稳定性测试脚本已加载');
