/**
 * 样式重构测试脚本
 * 🎯 核心价值：验证样式重构后的功能正确性和性能改善
 * 📦 功能范围：样式计算测试、类名生成测试、性能对比测试
 * 🔄 架构设计：自动化测试脚本，支持功能验证和性能监控
 */

import { StyleCalculationService, getStyleService } from '../core/services/StyleCalculationService';
import type { StyleCalculationOptions, StyleCalculationResult } from '../core/services/StyleCalculationService';

// ===== 测试配置 =====

interface StyleTestConfig {
  enableFunctionalTest: boolean;
  enablePerformanceTest: boolean;
  enableCacheTest: boolean;
  enableRegressionTest: boolean;
  verbose: boolean;
}

const DEFAULT_STYLE_TEST_CONFIG: StyleTestConfig = {
  enableFunctionalTest: true,
  enablePerformanceTest: true,
  enableCacheTest: true,
  enableRegressionTest: true,
  verbose: true
};

// ===== 测试工具 =====

class StyleTestRunner {
  private config: StyleTestConfig;
  private results: Map<string, any> = new Map();

  constructor(config: StyleTestConfig = DEFAULT_STYLE_TEST_CONFIG) {
    this.config = config;
  }

  log(message: string, data?: any): void {
    if (this.config.verbose) {
      console.log(`[样式测试] ${message}`, data || '');
    }
  }

  error(message: string, error?: any): void {
    console.error(`[样式测试错误] ${message}`, error || '');
  }

  async runTest(name: string, testFn: () => Promise<any>): Promise<boolean> {
    try {
      this.log(`开始测试: ${name}`);
      const startTime = performance.now();
      
      const result = await testFn();
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.results.set(name, {
        success: true,
        result,
        duration,
        timestamp: new Date().toISOString()
      });
      
      this.log(`测试完成: ${name} (${duration.toFixed(2)}ms)`, result);
      return true;
    } catch (error) {
      this.error(`测试失败: ${name}`, error);
      this.results.set(name, {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      });
      return false;
    }
  }

  getResults(): Map<string, any> {
    return this.results;
  }

  printSummary(): void {
    const total = this.results.size;
    const passed = Array.from(this.results.values()).filter(r => r.success).length;
    const failed = total - passed;

    console.log('\n=== 样式测试总结 ===');
    console.log(`总计: ${total} 个测试`);
    console.log(`通过: ${passed} 个测试`);
    console.log(`失败: ${failed} 个测试`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n失败的测试:');
      for (const [name, result] of this.results.entries()) {
        if (!result.success) {
          console.log(`- ${name}: ${result.error}`);
        }
      }
    }
  }
}

// ===== 样式计算服务测试 =====

async function testStyleCalculationService(): Promise<any> {
  const service = getStyleService();
  
  if (!service) {
    throw new Error('样式计算服务实例获取失败');
  }

  // 测试基础样式计算
  const basicOptions: StyleCalculationOptions = {
    coordinate: { x: 0, y: 0 },
    cellData: {
      isSelected: false,
      isHovered: false,
      isFocused: false,
      level: 1,
      color: 'red'
    },
    isEnhanced: false,
    isWordInputActive: false
  };

  const basicResult = service.calculateStyle(basicOptions);
  
  if (!basicResult.style || !basicResult.className) {
    throw new Error('基础样式计算结果无效');
  }

  // 测试增强模式
  const enhancedOptions: StyleCalculationOptions = {
    ...basicOptions,
    isEnhanced: true
  };

  const enhancedResult = service.calculateStyle(enhancedOptions);
  
  if (!enhancedResult.isSpecial || enhancedResult.priority <= basicResult.priority) {
    throw new Error('增强模式样式计算错误');
  }

  // 测试状态样式
  const selectedOptions: StyleCalculationOptions = {
    ...basicOptions,
    cellData: {
      ...basicOptions.cellData!,
      isSelected: true
    }
  };

  const selectedResult = service.calculateStyle(selectedOptions);
  
  if (!selectedResult.stateClasses.includes('selected')) {
    throw new Error('选中状态样式计算错误');
  }

  return {
    basicCalculation: !!basicResult.style,
    enhancedMode: enhancedResult.isSpecial,
    stateHandling: selectedResult.stateClasses.includes('selected'),
    classNameGeneration: basicResult.className.includes('matrix-cell'),
    priorityCalculation: enhancedResult.priority > basicResult.priority
  };
}

// ===== 类名生成测试 =====

async function testClassNameGeneration(): Promise<any> {
  const service = getStyleService();

  // 测试BEM规范
  const bemOptions: StyleCalculationOptions = {
    coordinate: { x: 1, y: 1 },
    cellData: {
      isSelected: true,
      isHovered: false,
      isFocused: false,
      level: 2,
      color: 'blue'
    },
    isEnhanced: true,
    useBEM: true
  };

  const bemResult = service.calculateStyle(bemOptions);
  const bemClassName = bemResult.className;

  // 检查BEM类名
  const hasBEMBase = bemClassName.includes('matrix-cell');
  const hasBEMModifier = bemClassName.includes('matrix-cell--');
  const hasEnhanced = bemClassName.includes('enhanced');
  const hasSelected = bemClassName.includes('selected');

  // 测试非BEM模式
  const nonBemOptions: StyleCalculationOptions = {
    ...bemOptions,
    useBEM: false
  };

  const nonBemResult = service.calculateStyle(nonBemOptions);
  const nonBemClassName = nonBemResult.className;

  // 检查非BEM类名
  const hasSimpleClasses = nonBemClassName.includes('enhanced') && 
                          nonBemClassName.includes('selected');

  return {
    bemGeneration: hasBEMBase && hasBEMModifier,
    bemModifiers: hasEnhanced && hasSelected,
    nonBemGeneration: hasSimpleClasses,
    classArrayLength: bemResult.classArray.length,
    modifiersLength: bemResult.modifiers.length
  };
}

// ===== 缓存性能测试 =====

async function testCachePerformance(): Promise<any> {
  const service = getStyleService();
  service.clearCache(); // 清除缓存开始测试

  const testOptions: StyleCalculationOptions = {
    coordinate: { x: 5, y: 5 },
    cellData: {
      isSelected: false,
      isHovered: false,
      isFocused: false,
      level: 3,
      color: 'green'
    },
    isEnhanced: false,
    isWordInputActive: false
  };

  const iterations = 1000;

  // 首次计算（无缓存）
  const firstCalculationStart = performance.now();
  service.calculateStyle(testOptions);
  const firstCalculationTime = performance.now() - firstCalculationStart;

  // 重复计算（有缓存）
  const cachedCalculationStart = performance.now();
  for (let i = 0; i < iterations; i++) {
    service.calculateStyle(testOptions);
  }
  const cachedCalculationTime = performance.now() - cachedCalculationStart;

  // 清除缓存后重新计算
  service.clearCache();
  const noCacheCalculationStart = performance.now();
  for (let i = 0; i < iterations; i++) {
    service.calculateStyle(testOptions);
  }
  const noCacheCalculationTime = performance.now() - noCacheCalculationStart;

  const cacheSpeedup = noCacheCalculationTime / cachedCalculationTime;
  const avgCachedTime = cachedCalculationTime / iterations;
  const avgNoCacheTime = noCacheCalculationTime / iterations;

  return {
    firstCalculationTime,
    avgCachedTime,
    avgNoCacheTime,
    cacheSpeedup,
    cacheEffective: cacheSpeedup > 2, // 缓存应该至少提升2倍性能
    cacheStats: service.getCacheStats()
  };
}

// ===== 样式规则测试 =====

async function testStyleRules(): Promise<any> {
  const service = getStyleService();

  // 测试添加自定义规则
  const customRule = {
    name: 'test-rule',
    priority: 25,
    enabled: true,
    condition: (options: StyleCalculationOptions) => options.coordinate.x === 10,
    applyStyle: () => ({ backgroundColor: 'yellow' }),
    applyClassName: () => ['test-custom-class']
  };

  service.addRule(customRule);

  // 测试自定义规则生效
  const testOptions: StyleCalculationOptions = {
    coordinate: { x: 10, y: 0 },
    cellData: {
      isSelected: false,
      isHovered: false,
      isFocused: false
    }
  };

  const result = service.calculateStyle(testOptions);
  const hasCustomStyle = result.style.backgroundColor === 'yellow';
  const hasCustomClass = result.className.includes('test-custom-class');

  // 测试规则禁用
  service.toggleRule('test-rule', false);
  const disabledResult = service.calculateStyle(testOptions);
  const ruleDisabled = disabledResult.style.backgroundColor !== 'yellow';

  // 测试规则移除
  service.removeRule('test-rule');
  const removedResult = service.calculateStyle(testOptions);
  const ruleRemoved = !removedResult.className.includes('test-custom-class');

  return {
    customRuleAdded: hasCustomStyle && hasCustomClass,
    ruleToggling: ruleDisabled,
    ruleRemoval: ruleRemoved,
    styleApplication: hasCustomStyle,
    classApplication: hasCustomClass
  };
}

// ===== 批量计算测试 =====

async function testBatchCalculation(): Promise<any> {
  const service = getStyleService();

  // 创建批量测试选项
  const batchOptions: StyleCalculationOptions[] = [];
  for (let x = 0; x < 10; x++) {
    for (let y = 0; y < 10; y++) {
      batchOptions.push({
        coordinate: { x, y },
        cellData: {
          isSelected: x === y,
          isHovered: false,
          isFocused: false,
          level: (x + y) % 4 + 1,
          color: ['red', 'blue', 'green', 'yellow'][x % 4]
        },
        isEnhanced: x % 3 === 0
      });
    }
  }

  // 批量计算
  const batchStart = performance.now();
  const batchResults = service.calculateBatchStyles(batchOptions);
  const batchTime = performance.now() - batchStart;

  // 单独计算对比
  const individualStart = performance.now();
  const individualResults = batchOptions.map(options => service.calculateStyle(options));
  const individualTime = performance.now() - individualStart;

  const batchEfficiency = individualTime / batchTime;

  return {
    batchCount: batchResults.length,
    batchTime,
    individualTime,
    batchEfficiency,
    resultsMatch: batchResults.length === individualResults.length,
    allResultsValid: batchResults.every(result => result.style && result.className)
  };
}

// ===== 主测试函数 =====

export async function runStyleRefactoringTests(config?: Partial<StyleTestConfig>): Promise<void> {
  const testConfig = { ...DEFAULT_STYLE_TEST_CONFIG, ...config };
  const runner = new StyleTestRunner(testConfig);

  console.log('🎨 开始样式重构测试...\n');

  if (testConfig.enableFunctionalTest) {
    await runner.runTest('样式计算服务', testStyleCalculationService);
    await runner.runTest('类名生成测试', testClassNameGeneration);
    await runner.runTest('样式规则测试', testStyleRules);
    await runner.runTest('批量计算测试', testBatchCalculation);
  }

  if (testConfig.enableCacheTest) {
    await runner.runTest('缓存性能测试', testCachePerformance);
  }

  runner.printSummary();

  // 输出详细结果
  if (testConfig.verbose) {
    console.log('\n=== 详细测试结果 ===');
    for (const [name, result] of runner.getResults().entries()) {
      console.log(`\n${name}:`, result);
    }
  }
}

// ===== 导出 =====

export { StyleTestRunner, testStyleCalculationService, testClassNameGeneration, testCachePerformance };

// 如果直接运行此脚本
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runStyleRefactoringTests = runStyleRefactoringTests;
  console.log('样式测试函数已挂载到 window.runStyleRefactoringTests');
}
