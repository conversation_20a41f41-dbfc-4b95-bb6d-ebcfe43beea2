/**
 * 事件处理重构测试脚本
 * 🎯 核心价值：验证事件处理重构后的功能正确性和性能改善
 * 📦 功能范围：事件处理测试、坐标计算测试、键盘导航测试、性能对比测试
 * 🔄 架构设计：自动化测试脚本，支持功能验证和性能监控
 */

import { EventHandlingService, getEventService } from '../core/services/EventHandlingService';
import type { EventHandlerOptions, CoordinateEventHandler } from '../core/services/EventHandlingService';

// ===== 测试配置 =====

interface EventTestConfig {
  enableFunctionalTest: boolean;
  enablePerformanceTest: boolean;
  enableKeyboardTest: boolean;
  enableFocusTest: boolean;
  verbose: boolean;
}

const DEFAULT_EVENT_TEST_CONFIG: EventTestConfig = {
  enableFunctionalTest: true,
  enablePerformanceTest: true,
  enableKeyboardTest: true,
  enableFocusTest: true,
  verbose: true
};

// ===== 测试工具 =====

class EventTestRunner {
  private config: EventTestConfig;
  private results: Map<string, any> = new Map();

  constructor(config: EventTestConfig = DEFAULT_EVENT_TEST_CONFIG) {
    this.config = config;
  }

  log(message: string, data?: any): void {
    if (this.config.verbose) {
      console.log(`[事件测试] ${message}`, data || '');
    }
  }

  error(message: string, error?: any): void {
    console.error(`[事件测试错误] ${message}`, error || '');
  }

  async runTest(name: string, testFn: () => Promise<any>): Promise<boolean> {
    try {
      this.log(`开始测试: ${name}`);
      const startTime = performance.now();
      
      const result = await testFn();
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.results.set(name, {
        success: true,
        result,
        duration,
        timestamp: new Date().toISOString()
      });
      
      this.log(`测试完成: ${name} (${duration.toFixed(2)}ms)`, result);
      return true;
    } catch (error) {
      this.error(`测试失败: ${name}`, error);
      this.results.set(name, {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      });
      return false;
    }
  }

  getResults(): Map<string, any> {
    return this.results;
  }

  printSummary(): void {
    const total = this.results.size;
    const passed = Array.from(this.results.values()).filter(r => r.success).length;
    const failed = total - passed;

    console.log('\n=== 事件处理测试总结 ===');
    console.log(`总计: ${total} 个测试`);
    console.log(`通过: ${passed} 个测试`);
    console.log(`失败: ${failed} 个测试`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n失败的测试:');
      for (const [name, result] of this.results.entries()) {
        if (!result.success) {
          console.log(`- ${name}: ${result.error}`);
        }
      }
    }
  }
}

// ===== 事件处理服务测试 =====

async function testEventHandlingService(): Promise<any> {
  const service = getEventService();
  
  if (!service) {
    throw new Error('事件处理服务实例获取失败');
  }

  // 创建测试DOM元素
  const testContainer = document.createElement('div');
  const testCell = document.createElement('div');
  testCell.setAttribute('data-x', '5');
  testCell.setAttribute('data-y', '3');
  testContainer.appendChild(testCell);
  document.body.appendChild(testContainer);

  try {
    let eventTriggered = false;
    let receivedCoordinate: any = null;

    // 测试坐标事件处理器
    const coordinateHandler: CoordinateEventHandler<MouseEvent> = (coordinate, event) => {
      eventTriggered = true;
      receivedCoordinate = coordinate;
    };

    const handler = service.createCoordinateHandler(coordinateHandler);

    // 模拟点击事件
    const clickEvent = new MouseEvent('click', { bubbles: true });
    Object.defineProperty(clickEvent, 'target', { value: testCell });
    
    handler(clickEvent);

    // 验证结果
    const coordinateExtracted = receivedCoordinate && 
      receivedCoordinate.x === 5 && 
      receivedCoordinate.y === 3;

    // 测试矩阵单元格事件处理器
    let matrixEventTriggered = false;
    let matrixX = 0, matrixY = 0;

    const matrixHandlers = service.createMatrixCellHandlers(2, 4, {
      onClick: (x, y, event) => {
        matrixEventTriggered = true;
        matrixX = x;
        matrixY = y;
      }
    });

    // 创建矩阵单元格测试元素
    const matrixCell = document.createElement('div');
    matrixCell.setAttribute('data-x', '2');
    matrixCell.setAttribute('data-y', '4');
    testContainer.appendChild(matrixCell);

    const matrixClickEvent = new MouseEvent('click', { bubbles: true });
    Object.defineProperty(matrixClickEvent, 'target', { value: matrixCell });
    
    matrixHandlers.onClick(matrixClickEvent);

    const matrixHandlerWorking = matrixEventTriggered && matrixX === 2 && matrixY === 4;

    return {
      serviceInstance: !!service,
      coordinateExtraction: coordinateExtracted,
      eventTriggering: eventTriggered,
      matrixHandlerCreation: !!matrixHandlers.onClick,
      matrixHandlerExecution: matrixHandlerWorking
    };
  } finally {
    // 清理DOM
    document.body.removeChild(testContainer);
  }
}

// ===== 键盘导航测试 =====

async function testKeyboardNavigation(): Promise<any> {
  const service = getEventService();

  let arrowUpTriggered = false;
  let arrowDownTriggered = false;
  let enterTriggered = false;
  let escapeTriggered = false;
  let customKeyTriggered = false;

  const keyboardHandler = service.createKeyboardNavigationHandler({
    onArrowUp: () => { arrowUpTriggered = true; },
    onArrowDown: () => { arrowDownTriggered = true; },
    onEnter: () => { enterTriggered = true; },
    onEscape: () => { escapeTriggered = true; },
    onCustomKey: (key) => { 
      if (key === 'test') {
        customKeyTriggered = true; 
      }
    }
  }, {
    enableArrowKeys: true,
    enableEnterConfirm: true,
    enableEscapeCancel: true,
    customKeyMap: {
      'Space': 'test'
    }
  });

  // 测试方向键
  const arrowUpEvent = new KeyboardEvent('keydown', { key: 'ArrowUp' });
  keyboardHandler(arrowUpEvent);

  const arrowDownEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' });
  keyboardHandler(arrowDownEvent);

  // 测试Enter键
  const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
  keyboardHandler(enterEvent);

  // 测试Escape键
  const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
  keyboardHandler(escapeEvent);

  // 测试自定义键
  const spaceEvent = new KeyboardEvent('keydown', { key: 'Space' });
  keyboardHandler(spaceEvent);

  return {
    arrowUpHandling: arrowUpTriggered,
    arrowDownHandling: arrowDownTriggered,
    enterHandling: enterTriggered,
    escapeHandling: escapeTriggered,
    customKeyHandling: customKeyTriggered,
    handlerCreation: !!keyboardHandler
  };
}

// ===== 防抖节流测试 =====

async function testDebounceThrottle(): Promise<any> {
  const service = getEventService();

  // 测试防抖
  let debounceCount = 0;
  const debounceHandler: CoordinateEventHandler<MouseEvent> = () => {
    debounceCount++;
  };

  const debouncedHandler = service.createCoordinateHandler(debounceHandler, {
    enableDebounce: true,
    debounceDelay: 50
  });

  // 创建测试元素
  const testElement = document.createElement('div');
  testElement.setAttribute('data-x', '1');
  testElement.setAttribute('data-y', '1');
  document.body.appendChild(testElement);

  try {
    // 快速触发多次事件
    for (let i = 0; i < 10; i++) {
      const event = new MouseEvent('click', { bubbles: true });
      Object.defineProperty(event, 'target', { value: testElement });
      debouncedHandler(event);
    }

    // 等待防抖完成
    await new Promise(resolve => setTimeout(resolve, 100));

    const debounceWorking = debounceCount === 1; // 防抖应该只执行一次

    // 测试节流
    let throttleCount = 0;
    const throttleHandler: CoordinateEventHandler<MouseEvent> = () => {
      throttleCount++;
    };

    const throttledHandler = service.createCoordinateHandler(throttleHandler, {
      enableThrottle: true,
      throttleDelay: 50
    });

    // 快速触发多次事件
    for (let i = 0; i < 10; i++) {
      const event = new MouseEvent('click', { bubbles: true });
      Object.defineProperty(event, 'target', { value: testElement });
      throttledHandler(event);
      
      // 小延迟确保节流生效
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    const throttleWorking = throttleCount >= 1 && throttleCount < 10; // 节流应该减少执行次数

    return {
      debounceWorking,
      debounceCount,
      throttleWorking,
      throttleCount
    };
  } finally {
    document.body.removeChild(testElement);
  }
}

// ===== 焦点管理测试 =====

async function testFocusManagement(): Promise<any> {
  const service = getEventService();

  // 创建测试元素
  const testElement1 = document.createElement('div');
  const testElement2 = document.createElement('div');
  
  testElement1.tabIndex = 0;
  testElement2.tabIndex = 0;
  
  document.body.appendChild(testElement1);
  document.body.appendChild(testElement2);

  try {
    // 测试自动聚焦
    const cleanup1 = service.manageFocus(testElement1, {
      autoFocus: true,
      focusDelay: 0
    });

    const autoFocusWorking = document.activeElement === testElement1;

    // 测试循环聚焦
    const cleanup2 = service.manageFocus(testElement2, {
      cycleFocus: true
    });

    const focusManagementWorking = typeof cleanup1 === 'function' || cleanup1 === undefined;

    // 清理
    if (typeof cleanup1 === 'function') cleanup1();
    if (typeof cleanup2 === 'function') cleanup2();

    return {
      autoFocusWorking,
      focusManagementWorking,
      cleanupFunctions: {
        cleanup1: typeof cleanup1,
        cleanup2: typeof cleanup2
      }
    };
  } finally {
    document.body.removeChild(testElement1);
    document.body.removeChild(testElement2);
  }
}

// ===== 性能对比测试 =====

async function testEventPerformance(): Promise<any> {
  const service = getEventService();
  const iterations = 1000;

  // 创建测试元素
  const testElement = document.createElement('div');
  testElement.setAttribute('data-x', '10');
  testElement.setAttribute('data-y', '10');
  document.body.appendChild(testElement);

  try {
    // 测试统一事件处理器性能
    let unifiedCount = 0;
    const unifiedHandler = service.createCoordinateHandler((coordinate, event) => {
      unifiedCount++;
    });

    const unifiedStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      const event = new MouseEvent('click', { bubbles: true });
      Object.defineProperty(event, 'target', { value: testElement });
      unifiedHandler(event);
    }
    const unifiedTime = performance.now() - unifiedStart;

    // 测试传统事件处理器性能
    let traditionalCount = 0;
    const traditionalHandler = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const x = parseInt(target.getAttribute('data-x') || '0', 10);
      const y = parseInt(target.getAttribute('data-y') || '0', 10);
      traditionalCount++;
    };

    const traditionalStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      const event = new MouseEvent('click', { bubbles: true });
      Object.defineProperty(event, 'target', { value: testElement });
      traditionalHandler(event);
    }
    const traditionalTime = performance.now() - traditionalStart;

    const performanceRatio = traditionalTime / unifiedTime;

    return {
      unifiedTime,
      traditionalTime,
      performanceRatio,
      unifiedCount,
      traditionalCount,
      iterations,
      avgUnifiedTime: unifiedTime / iterations,
      avgTraditionalTime: traditionalTime / iterations
    };
  } finally {
    document.body.removeChild(testElement);
  }
}

// ===== 主测试函数 =====

export async function runEventHandlingTests(config?: Partial<EventTestConfig>): Promise<void> {
  const testConfig = { ...DEFAULT_EVENT_TEST_CONFIG, ...config };
  const runner = new EventTestRunner(testConfig);

  console.log('⚡ 开始事件处理重构测试...\n');

  if (testConfig.enableFunctionalTest) {
    await runner.runTest('事件处理服务', testEventHandlingService);
    await runner.runTest('防抖节流测试', testDebounceThrottle);
  }

  if (testConfig.enableKeyboardTest) {
    await runner.runTest('键盘导航测试', testKeyboardNavigation);
  }

  if (testConfig.enableFocusTest) {
    await runner.runTest('焦点管理测试', testFocusManagement);
  }

  if (testConfig.enablePerformanceTest) {
    await runner.runTest('事件性能测试', testEventPerformance);
  }

  runner.printSummary();

  // 输出详细结果
  if (testConfig.verbose) {
    console.log('\n=== 详细测试结果 ===');
    for (const [name, result] of runner.getResults().entries()) {
      console.log(`\n${name}:`, result);
    }
  }
}

// ===== 导出 =====

export { EventTestRunner, testEventHandlingService, testKeyboardNavigation, testDebounceThrottle };

// 如果直接运行此脚本
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runEventHandlingTests = runEventHandlingTests;
  console.log('事件测试函数已挂载到 window.runEventHandlingTests');
}
