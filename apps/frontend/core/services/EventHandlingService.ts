/**
 * 统一事件处理服务
 * 🎯 核心价值：整合所有事件处理逻辑，消除重复代码，提供一致的交互体验
 * 📦 功能范围：事件委托、坐标计算、键盘导航、防抖节流、焦点管理
 * 🔄 架构设计：单例服务模式，支持事件插件和性能优化
 */

import type { Coordinate } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

export interface EventHandlerOptions {
  /** 是否启用防抖 */
  enableDebounce?: boolean;
  /** 防抖延迟 */
  debounceDelay?: number;
  /** 是否启用节流 */
  enableThrottle?: boolean;
  /** 节流延迟 */
  throttleDelay?: number;
  /** 是否阻止默认行为 */
  preventDefault?: boolean;
  /** 是否阻止事件冒泡 */
  stopPropagation?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

export interface CoordinateEventHandler<T = Event> {
  (coordinate: Coordinate, event: T): void;
}

export interface KeyboardNavigationOptions {
  /** 是否启用方向键导航 */
  enableArrowKeys?: boolean;
  /** 是否启用Tab导航 */
  enableTabNavigation?: boolean;
  /** 是否启用Enter确认 */
  enableEnterConfirm?: boolean;
  /** 是否启用Escape取消 */
  enableEscapeCancel?: boolean;
  /** 自定义键盘映射 */
  customKeyMap?: Record<string, string>;
}

export interface FocusManagementOptions {
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 聚焦延迟 */
  focusDelay?: number;
  /** 是否循环聚焦 */
  cycleFocus?: boolean;
  /** 聚焦选择器 */
  focusSelector?: string;
}

export interface EventDelegationOptions {
  /** 委托容器选择器 */
  containerSelector?: string;
  /** 目标元素选择器 */
  targetSelector?: string;
  /** 是否使用事件委托 */
  useDelegation?: boolean;
}

export interface EventMetrics {
  /** 事件触发次数 */
  triggerCount: number;
  /** 平均处理时间 */
  averageProcessTime: number;
  /** 最后触发时间 */
  lastTriggerTime: number;
  /** 防抖/节流生效次数 */
  optimizationCount: number;
}

// ===== 事件处理器接口 =====

export interface EventHandler<T = Event> {
  name: string;
  handler: (event: T) => void;
  options: EventHandlerOptions;
  enabled: boolean;
  metrics: EventMetrics;
}

export interface CoordinateEventHandlers {
  onClick?: CoordinateEventHandler<MouseEvent>;
  onDoubleClick?: CoordinateEventHandler<MouseEvent>;
  onMouseEnter?: CoordinateEventHandler<MouseEvent>;
  onMouseLeave?: CoordinateEventHandler<MouseEvent>;
  onMouseDown?: CoordinateEventHandler<MouseEvent>;
  onMouseUp?: CoordinateEventHandler<MouseEvent>;
  onFocus?: CoordinateEventHandler<FocusEvent>;
  onBlur?: CoordinateEventHandler<FocusEvent>;
  onKeyDown?: CoordinateEventHandler<KeyboardEvent>;
  onKeyUp?: CoordinateEventHandler<KeyboardEvent>;
}

// ===== 统一事件处理服务 =====

export class EventHandlingService {
  private static instance: EventHandlingService;
  private eventHandlers: Map<string, EventHandler> = new Map();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private throttleTimers: Map<string, boolean> = new Map();
  private focusStack: HTMLElement[] = [];
  private debugMode = false;

  private constructor() { }

  /**
   * 获取服务实例
   */
  static getInstance(): EventHandlingService {
    if (!EventHandlingService.instance) {
      EventHandlingService.instance = new EventHandlingService();
    }
    return EventHandlingService.instance;
  }

  /**
   * 设置调试模式
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  /**
   * 创建坐标事件处理器
   */
  createCoordinateHandler<T extends Event>(
    handler: CoordinateEventHandler<T>,
    options: EventHandlerOptions = {}
  ): (event: T) => void {
    const {
      enableDebounce = false,
      debounceDelay = 150,
      enableThrottle = false,
      throttleDelay = 100,
      preventDefault = false,
      stopPropagation = false,
      debug = false
    } = options;

    return (event: T) => {
      const startTime = performance.now();

      // 阻止默认行为和事件冒泡
      if (preventDefault) {
        event.preventDefault();
      }
      if (stopPropagation) {
        event.stopPropagation();
      }

      // 提取坐标信息
      const coordinate = this.extractCoordinateFromEvent(event);
      if (!coordinate) {
        if (debug || this.debugMode) {
          console.warn('[EventHandling] 无法从事件中提取坐标信息', event);
        }
        return;
      }

      const handlerKey = `${coordinate.x},${coordinate.y}`;

      // 防抖处理
      if (enableDebounce) {
        const existingTimer = this.debounceTimers.get(handlerKey);
        if (existingTimer) {
          clearTimeout(existingTimer);
        }

        const timer = setTimeout(() => {
          this.executeHandler(handler, coordinate, event, startTime, debug);
          this.debounceTimers.delete(handlerKey);
        }, debounceDelay);

        this.debounceTimers.set(handlerKey, timer);
        return;
      }

      // 节流处理
      if (enableThrottle) {
        if (this.throttleTimers.get(handlerKey)) {
          return; // 节流中，忽略事件
        }

        this.throttleTimers.set(handlerKey, true);
        setTimeout(() => {
          this.throttleTimers.delete(handlerKey);
        }, throttleDelay);
      }

      // 直接执行
      this.executeHandler(handler, coordinate, event, startTime, debug);
    };
  }

  /**
   * 执行事件处理器
   */
  private executeHandler<T extends Event>(
    handler: CoordinateEventHandler<T>,
    coordinate: Coordinate,
    event: T,
    startTime: number,
    debug: boolean
  ): void {
    try {
      handler(coordinate, event);

      if (debug || this.debugMode) {
        const duration = performance.now() - startTime;
        console.log(`[EventHandling] 事件处理完成: ${coordinate.x},${coordinate.y} (${duration.toFixed(2)}ms)`);
      }
    } catch (error) {
      console.error('[EventHandling] 事件处理器执行失败:', error);
    }
  }

  /**
   * 从事件中提取坐标信息
   */
  private extractCoordinateFromEvent(event: Event): Coordinate | null {
    const target = event.target as HTMLElement;
    if (!target) return null;

    // 尝试从data属性获取坐标
    const x = target.dataset.x || target.getAttribute('data-x');
    const y = target.dataset.y || target.getAttribute('data-y');

    if (x !== null && y !== null) {
      return {
        x: parseInt(x, 10),
        y: parseInt(y, 10)
      };
    }

    // 尝试从父元素获取坐标
    let parent = target.parentElement;
    while (parent) {
      const parentX = parent.dataset.x || parent.getAttribute('data-x');
      const parentY = parent.dataset.y || parent.getAttribute('data-y');

      if (parentX !== null && parentY !== null) {
        return {
          x: parseInt(parentX, 10),
          y: parseInt(parentY, 10)
        };
      }

      parent = parent.parentElement;
    }

    return null;
  }

  /**
   * 创建键盘导航处理器
   */
  createKeyboardNavigationHandler(
    handlers: {
      onArrowUp?: () => void;
      onArrowDown?: () => void;
      onArrowLeft?: () => void;
      onArrowRight?: () => void;
      onEnter?: () => void;
      onEscape?: () => void;
      onTab?: () => void;
      onCustomKey?: (key: string) => void;
    },
    options: KeyboardNavigationOptions = {}
  ): (event: KeyboardEvent) => void {
    const {
      enableArrowKeys = true,
      enableTabNavigation = true,
      enableEnterConfirm = true,
      enableEscapeCancel = true,
      customKeyMap = {}
    } = options;

    return (event: KeyboardEvent) => {
      const key = event.key;
      let handled = false;

      // 方向键导航
      if (enableArrowKeys) {
        switch (key) {
          case 'ArrowUp':
            event.preventDefault();
            handlers.onArrowUp?.();
            handled = true;
            break;
          case 'ArrowDown':
            event.preventDefault();
            handlers.onArrowDown?.();
            handled = true;
            break;
          case 'ArrowLeft':
            event.preventDefault();
            handlers.onArrowLeft?.();
            handled = true;
            break;
          case 'ArrowRight':
            event.preventDefault();
            handlers.onArrowRight?.();
            handled = true;
            break;
        }
      }

      // Tab导航
      if (enableTabNavigation && key === 'Tab') {
        handlers.onTab?.();
        handled = true;
      }

      // Enter确认
      if (enableEnterConfirm && key === 'Enter') {
        event.preventDefault();
        handlers.onEnter?.();
        handled = true;
      }

      // Escape取消
      if (enableEscapeCancel && key === 'Escape') {
        event.preventDefault();
        handlers.onEscape?.();
        handled = true;
      }

      // 自定义键盘映射
      if (customKeyMap[key]) {
        event.preventDefault();
        handlers.onCustomKey?.(customKeyMap[key]);
        handled = true;
      }

      if (this.debugMode && handled) {
        console.log(`[EventHandling] 键盘事件处理: ${key}`);
      }
    };
  }

  /**
   * 焦点管理
   */
  manageFocus(
    element: HTMLElement,
    options: FocusManagementOptions = {}
  ): void {
    const {
      autoFocus = false,
      focusDelay = 0,
      cycleFocus = false
    } = options;

    if (autoFocus) {
      if (focusDelay > 0) {
        setTimeout(() => {
          element.focus();
        }, focusDelay);
      } else {
        element.focus();
      }
    }

    // 添加到焦点栈
    this.focusStack.push(element);

    // 如果启用循环聚焦，设置Tab键处理
    if (cycleFocus) {
      const handleTabKey = (event: KeyboardEvent) => {
        if (event.key === 'Tab') {
          event.preventDefault();
          this.cycleFocusToNext(event.shiftKey);
        }
      };

      element.addEventListener('keydown', handleTabKey);

      // 清理函数
      return () => {
        element.removeEventListener('keydown', handleTabKey);
        this.removeFocusFromStack(element);
      };
    }
  }

  /**
   * 循环聚焦到下一个元素
   */
  private cycleFocusToNext(reverse: boolean = false): void {
    if (this.focusStack.length === 0) return;

    const currentIndex = this.focusStack.findIndex(el => el === document.activeElement);
    let nextIndex: number;

    if (reverse) {
      nextIndex = currentIndex <= 0 ? this.focusStack.length - 1 : currentIndex - 1;
    } else {
      nextIndex = currentIndex >= this.focusStack.length - 1 ? 0 : currentIndex + 1;
    }

    this.focusStack[nextIndex]?.focus();
  }

  /**
   * 从焦点栈中移除元素
   */
  private removeFocusFromStack(element: HTMLElement): void {
    const index = this.focusStack.indexOf(element);
    if (index > -1) {
      this.focusStack.splice(index, 1);
    }
  }

  /**
   * 创建事件委托处理器
   */
  createEventDelegation(
    container: HTMLElement,
    eventType: string,
    targetSelector: string,
    handler: (target: HTMLElement, event: Event) => void,
    options: EventHandlerOptions = {}
  ): () => void {
    const delegationHandler = (event: Event) => {
      const target = event.target as HTMLElement;
      const matchedTarget = target.closest(targetSelector) as HTMLElement;

      if (matchedTarget && container.contains(matchedTarget)) {
        if (options.preventDefault) {
          event.preventDefault();
        }
        if (options.stopPropagation) {
          event.stopPropagation();
        }

        handler(matchedTarget, event);
      }
    };

    container.addEventListener(eventType, delegationHandler);

    // 返回清理函数
    return () => {
      container.removeEventListener(eventType, delegationHandler);
    };
  }

  /**
   * 清理所有定时器
   */
  cleanup(): void {
    // 清理防抖定时器
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();

    // 清理节流标记
    this.throttleTimers.clear();

    // 清理焦点栈
    this.focusStack.length = 0;
  }

  /**
   * 获取事件处理统计
   */
  getEventMetrics(): Map<string, EventMetrics> {
    const metrics = new Map<string, EventMetrics>();

    for (const [name, handler] of this.eventHandlers.entries()) {
      metrics.set(name, { ...handler.metrics });
    }

    return metrics;
  }

  /**
   * 批量创建坐标事件处理器
   */
  createCoordinateHandlers(
    handlers: CoordinateEventHandlers,
    options: EventHandlerOptions = {}
  ): Record<string, (event: Event) => void> {
    const result: Record<string, (event: Event) => void> = {};

    if (handlers.onClick) {
      result.onClick = this.createCoordinateHandler(handlers.onClick, options);
    }
    if (handlers.onDoubleClick) {
      result.onDoubleClick = this.createCoordinateHandler(handlers.onDoubleClick, options);
    }
    if (handlers.onMouseEnter) {
      result.onMouseEnter = this.createCoordinateHandler(handlers.onMouseEnter, options);
    }
    if (handlers.onMouseLeave) {
      result.onMouseLeave = this.createCoordinateHandler(handlers.onMouseLeave, options);
    }
    if (handlers.onMouseDown) {
      result.onMouseDown = this.createCoordinateHandler(handlers.onMouseDown, options);
    }
    if (handlers.onMouseUp) {
      result.onMouseUp = this.createCoordinateHandler(handlers.onMouseUp, options);
    }
    if (handlers.onFocus) {
      result.onFocus = this.createCoordinateHandler(handlers.onFocus, options);
    }
    if (handlers.onBlur) {
      result.onBlur = this.createCoordinateHandler(handlers.onBlur, options);
    }
    if (handlers.onKeyDown) {
      result.onKeyDown = this.createCoordinateHandler(handlers.onKeyDown, options);
    }
    if (handlers.onKeyUp) {
      result.onKeyUp = this.createCoordinateHandler(handlers.onKeyUp, options);
    }

    return result;
  }

  /**
   * 创建矩阵单元格事件处理器
   */
  createMatrixCellHandlers(
    x: number,
    y: number,
    handlers: {
      onClick?: (x: number, y: number, event: MouseEvent) => void;
      onDoubleClick?: (x: number, y: number, event: MouseEvent) => void;
      onMouseEnter?: (x: number, y: number, event: MouseEvent) => void;
      onMouseLeave?: (x: number, y: number, event: MouseEvent) => void;
      onFocus?: (x: number, y: number, event: FocusEvent) => void;
      onBlur?: (x: number, y: number, event: FocusEvent) => void;
    },
    options: EventHandlerOptions = {}
  ): Record<string, (event: Event) => void> {
    const coordinateHandlers: CoordinateEventHandlers = {};

    if (handlers.onClick) {
      coordinateHandlers.onClick = (coordinate, event) => {
        handlers.onClick!(coordinate.x, coordinate.y, event as MouseEvent);
      };
    }
    if (handlers.onDoubleClick) {
      coordinateHandlers.onDoubleClick = (coordinate, event) => {
        handlers.onDoubleClick!(coordinate.x, coordinate.y, event as MouseEvent);
      };
    }
    if (handlers.onMouseEnter) {
      coordinateHandlers.onMouseEnter = (coordinate, event) => {
        handlers.onMouseEnter!(coordinate.x, coordinate.y, event as MouseEvent);
      };
    }
    if (handlers.onMouseLeave) {
      coordinateHandlers.onMouseLeave = (coordinate, event) => {
        handlers.onMouseLeave!(coordinate.x, coordinate.y, event as MouseEvent);
      };
    }
    if (handlers.onFocus) {
      coordinateHandlers.onFocus = (coordinate, event) => {
        handlers.onFocus!(coordinate.x, coordinate.y, event as FocusEvent);
      };
    }
    if (handlers.onBlur) {
      coordinateHandlers.onBlur = (coordinate, event) => {
        handlers.onBlur!(coordinate.x, coordinate.y, event as FocusEvent);
      };
    }

    return this.createCoordinateHandlers(coordinateHandlers, options);
  }
}

// ===== 便捷方法 =====

/**
 * 获取事件处理服务实例
 */
export const getEventService = () => EventHandlingService.getInstance();

/**
 * 快速创建坐标事件处理器
 */
export const createCoordinateHandler = <T extends Event>(
  handler: CoordinateEventHandler<T>,
  options?: EventHandlerOptions
) => getEventService().createCoordinateHandler(handler, options);

/**
 * 快速创建键盘导航处理器
 */
export const createKeyboardNavigationHandler = (
  handlers: Parameters<EventHandlingService['createKeyboardNavigationHandler']>[0],
  options?: KeyboardNavigationOptions
) => getEventService().createKeyboardNavigationHandler(handlers, options);

/**
 * 快速创建矩阵单元格事件处理器
 */
export const createMatrixCellHandlers = (
  x: number,
  y: number,
  handlers: Parameters<EventHandlingService['createMatrixCellHandlers']>[2],
  options?: EventHandlerOptions
) => getEventService().createMatrixCellHandlers(x, y, handlers, options);

// ===== 工具函数 =====

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * 节流函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let isThrottled = false;

  return (...args: Parameters<T>) => {
    if (isThrottled) return;

    func(...args);
    isThrottled = true;

    setTimeout(() => {
      isThrottled = false;
    }, delay);
  };
};

/**
 * 组合事件处理器
 */
export const combineEventHandlers = <T extends Event>(
  ...handlers: Array<(event: T) => void>
): ((event: T) => void) => {
  return (event: T) => {
    for (const handler of handlers) {
      try {
        handler(event);
      } catch (error) {
        console.error('[EventHandling] 组合事件处理器执行失败:', error);
      }
    }
  };
};

/**
 * 条件事件处理器
 */
export const conditionalEventHandler = <T extends Event>(
  condition: (event: T) => boolean,
  handler: (event: T) => void,
  elseHandler?: (event: T) => void
): ((event: T) => void) => {
  return (event: T) => {
    if (condition(event)) {
      handler(event);
    } else if (elseHandler) {
      elseHandler(event);
    }
  };
};

/**
 * 一次性事件处理器
 */
export const onceEventHandler = <T extends Event>(
  handler: (event: T) => void
): ((event: T) => void) => {
  let hasBeenCalled = false;

  return (event: T) => {
    if (!hasBeenCalled) {
      hasBeenCalled = true;
      handler(event);
    }
  };
};

/**
 * 事件处理器链
 */
export const createEventHandlerChain = <T extends Event>() => {
  const handlers: Array<(event: T) => void> = [];

  const chain = {
    add: (handler: (event: T) => void) => {
      handlers.push(handler);
      return chain;
    },

    addIf: (condition: boolean, handler: (event: T) => void) => {
      if (condition) {
        handlers.push(handler);
      }
      return chain;
    },

    build: (): ((event: T) => void) => {
      return combineEventHandlers(...handlers);
    }
  };

  return chain;
};

// ===== 导出类型 =====
export type {
  CoordinateEventHandler,
  CoordinateEventHandlers, EventDelegationOptions, EventHandler, EventHandlerOptions, EventMetrics, FocusManagementOptions, KeyboardNavigationOptions
};

