/**
 * 统一滚动管理服务
 * 🎯 核心价值：统一所有滚动相关逻辑，消除重复代码，提供一致的滚动体验
 * 📦 功能范围：滚动容器管理、位置计算、滚动执行、状态跟踪
 * 🔄 架构设计：单例服务模式，提供静态方法和实例方法
 */

// ===== 类型定义 =====

export interface ScrollPosition {
  /** 当前滚动位置 */
  current: number;
  /** 目标滚动位置 */
  target: number;
  /** 位置差值 */
  delta: number;
  /** 滚动方向 */
  direction: 'up' | 'down' | 'none';
}

export interface ScrollOptions {
  /** 滚动行为 */
  behavior?: ScrollBehavior;
  /** 对齐方式 */
  block?: ScrollLogicalPosition;
  /** 是否强制滚动 */
  force?: boolean;
  /** 调试模式 */
  debug?: boolean;
}

export interface ScrollResult {
  /** 是否成功执行滚动 */
  success: boolean;
  /** 滚动位置信息 */
  position: ScrollPosition;
  /** 错误信息 */
  error?: string;
  /** 执行时间 */
  executionTime: number;
}

export interface ScrollContainerInfo {
  /** 容器元素 */
  element: HTMLElement;
  /** 容器类型 */
  type: 'auto' | 'scroll' | 'custom';
  /** 可滚动高度 */
  scrollableHeight: number;
  /** 当前滚动位置 */
  currentPosition: number;
}

// ===== 滚动管理服务 =====

export class ScrollManagementService {
  private static instance: ScrollManagementService;
  private containerCache = new Map<string, HTMLElement>();
  private positionHistory = new Map<string, number>();
  private debugMode = false;

  private constructor() {}

  /**
   * 获取服务实例
   */
  static getInstance(): ScrollManagementService {
    if (!ScrollManagementService.instance) {
      ScrollManagementService.instance = new ScrollManagementService();
    }
    return ScrollManagementService.instance;
  }

  /**
   * 设置调试模式
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  /**
   * 查找滚动容器
   */
  static findScrollContainer(
    element: HTMLElement,
    customSelector?: string
  ): HTMLElement | null {
    // 如果提供了自定义选择器，优先使用
    if (customSelector) {
      const customContainer = element.closest(customSelector) as HTMLElement;
      if (customContainer) return customContainer;
    }

    // 自动查找最近的滚动容器
    let parent = element.parentElement;
    while (parent) {
      const style = window.getComputedStyle(parent);
      const overflowY = style.overflowY;
      
      if (overflowY === 'auto' || overflowY === 'scroll' || overflowY === 'overlay') {
        return parent;
      }
      
      parent = parent.parentElement;
    }

    // 如果没找到，返回document.documentElement
    return document.documentElement;
  }

  /**
   * 获取容器信息
   */
  static getContainerInfo(container: HTMLElement): ScrollContainerInfo {
    const style = window.getComputedStyle(container);
    
    return {
      element: container,
      type: style.overflowY as 'auto' | 'scroll' | 'custom',
      scrollableHeight: container.scrollHeight - container.clientHeight,
      currentPosition: container.scrollTop
    };
  }

  /**
   * 计算滚动位置
   */
  static calculateScrollPosition(
    targetElement: HTMLElement,
    container: HTMLElement,
    block: ScrollLogicalPosition = 'center'
  ): ScrollPosition {
    const current = container.scrollTop;
    const targetRect = targetElement.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    let target: number;

    switch (block) {
      case 'start':
        target = current + (targetRect.top - containerRect.top);
        break;
      case 'end':
        target = current + (targetRect.bottom - containerRect.bottom);
        break;
      case 'center':
      default:
        const targetCenter = targetRect.top + targetRect.height / 2;
        const containerCenter = containerRect.top + containerRect.height / 2;
        target = current + (targetCenter - containerCenter);
        break;
      case 'nearest':
        // 如果目标已经在视口内，不滚动
        if (targetRect.top >= containerRect.top && targetRect.bottom <= containerRect.bottom) {
          target = current;
        } else {
          // 选择最近的边缘
          const distanceToTop = Math.abs(targetRect.top - containerRect.top);
          const distanceToBottom = Math.abs(targetRect.bottom - containerRect.bottom);
          
          if (distanceToTop < distanceToBottom) {
            target = current + (targetRect.top - containerRect.top);
          } else {
            target = current + (targetRect.bottom - containerRect.bottom);
          }
        }
        break;
    }

    // 确保目标位置在有效范围内
    const maxScroll = container.scrollHeight - container.clientHeight;
    target = Math.max(0, Math.min(target, maxScroll));

    const delta = target - current;
    const direction = delta === 0 ? 'none' : delta < 0 ? 'up' : 'down';

    return {
      current,
      target,
      delta,
      direction
    };
  }

  /**
   * 执行滚动
   */
  static executeScroll(
    container: HTMLElement,
    position: ScrollPosition,
    options: ScrollOptions = {}
  ): Promise<ScrollResult> {
    const startTime = performance.now();
    const { behavior = 'smooth', debug = false } = options;

    return new Promise((resolve) => {
      if (position.delta === 0) {
        const result: ScrollResult = {
          success: false,
          position,
          error: '无需滚动：目标已在正确位置',
          executionTime: performance.now() - startTime
        };
        
        if (debug) {
          console.log('[ScrollManagementService] 无需滚动', result);
        }
        
        resolve(result);
        return;
      }

      try {
        container.scrollTo({
          top: position.target,
          behavior
        });

        const result: ScrollResult = {
          success: true,
          position,
          executionTime: performance.now() - startTime
        };

        if (debug) {
          console.log('[ScrollManagementService] 滚动执行成功', {
            direction: position.direction,
            delta: position.delta,
            result
          });
        }

        resolve(result);
      } catch (error) {
        const result: ScrollResult = {
          success: false,
          position,
          error: error instanceof Error ? error.message : '滚动执行失败',
          executionTime: performance.now() - startTime
        };

        if (debug) {
          console.error('[ScrollManagementService] 滚动执行失败', error);
        }

        resolve(result);
      }
    });
  }

  /**
   * 滚动到目标元素（完整流程）
   */
  static async scrollToElement(
    targetElement: HTMLElement,
    options: ScrollOptions & { containerSelector?: string } = {}
  ): Promise<ScrollResult> {
    const { containerSelector, ...scrollOptions } = options;
    
    // 查找容器
    const container = this.findScrollContainer(targetElement, containerSelector);
    if (!container) {
      return {
        success: false,
        position: { current: 0, target: 0, delta: 0, direction: 'none' },
        error: '未找到滚动容器',
        executionTime: 0
      };
    }

    // 计算位置
    const position = this.calculateScrollPosition(targetElement, container, options.block);
    
    // 执行滚动
    return this.executeScroll(container, position, scrollOptions);
  }

  /**
   * 保存滚动位置
   */
  saveScrollPosition(key: string, container: HTMLElement): void {
    this.positionHistory.set(key, container.scrollTop);
    
    if (this.debugMode) {
      console.log(`[ScrollManagementService] 保存滚动位置: ${key} = ${container.scrollTop}`);
    }
  }

  /**
   * 恢复滚动位置
   */
  restoreScrollPosition(key: string, container: HTMLElement): boolean {
    const savedPosition = this.positionHistory.get(key);
    
    if (savedPosition !== undefined) {
      container.scrollTop = savedPosition;
      
      if (this.debugMode) {
        console.log(`[ScrollManagementService] 恢复滚动位置: ${key} = ${savedPosition}`);
      }
      
      return true;
    }
    
    return false;
  }

  /**
   * 清除位置历史
   */
  clearPositionHistory(key?: string): void {
    if (key) {
      this.positionHistory.delete(key);
    } else {
      this.positionHistory.clear();
    }
  }

  /**
   * 缓存容器
   */
  cacheContainer(key: string, container: HTMLElement): void {
    this.containerCache.set(key, container);
  }

  /**
   * 获取缓存的容器
   */
  getCachedContainer(key: string): HTMLElement | undefined {
    return this.containerCache.get(key);
  }

  /**
   * 清除容器缓存
   */
  clearContainerCache(key?: string): void {
    if (key) {
      this.containerCache.delete(key);
    } else {
      this.containerCache.clear();
    }
  }
}

// ===== 便捷方法 =====

/**
 * 获取滚动管理服务实例
 */
export const getScrollService = () => ScrollManagementService.getInstance();

/**
 * 快速滚动到元素
 */
export const scrollToElement = (
  targetElement: HTMLElement,
  options?: ScrollOptions & { containerSelector?: string }
) => ScrollManagementService.scrollToElement(targetElement, options);

/**
 * 查找滚动容器
 */
export const findScrollContainer = (
  element: HTMLElement,
  customSelector?: string
) => ScrollManagementService.findScrollContainer(element, customSelector);

// ===== 导出类型 =====
export type {
  ScrollPosition,
  ScrollOptions,
  ScrollResult,
  ScrollContainerInfo
};
