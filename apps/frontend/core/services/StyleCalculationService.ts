/**
 * 统一样式计算服务
 * 🎯 核心价值：整合所有样式和类名计算逻辑，消除重复代码，提供一致的样式体验
 * 📦 功能范围：类名生成、样式计算、BEM规范、主题支持、缓存优化
 * 🔄 架构设计：单例服务模式，支持插件化样式规则和缓存机制
 */

import { getCellStyle } from '@/core/matrix/MatrixConfig';
import type {
  CellData,
  CellRenderData,
  Coordinate,
  MatrixConfig
} from '@/core/matrix/MatrixTypes';

// ===== 统一类型定义 =====

export interface StyleCalculationOptions {
  /** 单元格坐标 */
  coordinate: Coordinate;
  /** 单元格数据 */
  cellData?: CellData;
  /** 渲染数据 */
  renderData?: CellRenderData;
  /** 矩阵配置 */
  config?: MatrixConfig;
  /** 是否为增强模式 */
  isEnhanced?: boolean;
  /** 是否为填词模式活跃状态 */
  isWordInputActive?: boolean;
  /** 自定义样式 */
  customStyle?: React.CSSProperties;
  /** 自定义类名 */
  customClassName?: string;
  /** 条件类名映射 */
  conditionalClasses?: Record<string, boolean>;
  /** 是否使用BEM规范 */
  useBEM?: boolean;
  /** 主题配置 */
  theme?: StyleTheme;
}

export interface StyleTheme {
  primaryColor?: string;
  secondaryColor?: string;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
}

export interface StyleCalculationResult {
  /** 完整的CSS样式 */
  style: React.CSSProperties;
  /** 完整的CSS类名字符串 */
  className: string;
  /** 类名数组 */
  classArray: string[];
  /** BEM修饰符 */
  modifiers: string[];
  /** 状态类名 */
  stateClasses: string[];
  /** 是否需要特殊处理 */
  isSpecial: boolean;
  /** 样式优先级 */
  priority: number;
}

export interface StyleRule {
  name: string;
  condition: (options: StyleCalculationOptions) => boolean;
  applyStyle: (options: StyleCalculationOptions) => React.CSSProperties;
  applyClassName: (options: StyleCalculationOptions) => string[];
  priority: number;
  enabled: boolean;
}

// ===== 常量定义 =====

const DEFAULT_BASE_CLASS = 'matrix-cell';

const DEFAULT_THEME: StyleTheme = {
  primaryColor: '#007bff',
  secondaryColor: '#6c757d',
  backgroundColor: '#ffffff',
  textColor: '#000000',
  borderColor: '#dee2e6',
};

const BEM_MODIFIERS = {
  enhanced: 'enhanced',
  wordInputActive: 'word-input-active',
  selected: 'selected',
  hovered: 'hovered',
  focused: 'focused',
  level1: 'level-1',
  level2: 'level-2',
  level3: 'level-3',
  level4: 'level-4',
  colorMode: 'color-mode',
  wordMode: 'word-mode',
  numberMode: 'number-mode',
  coordinateMode: 'coordinate-mode',
} as const;

const COLOR_CLASSES = {
  red: 'color-red',
  blue: 'color-blue',
  green: 'color-green',
  yellow: 'color-yellow',
  purple: 'color-purple',
  orange: 'color-orange',
  pink: 'color-pink',
  cyan: 'color-cyan',
  black: 'color-black',
  white: 'color-white',
  gray: 'color-gray',
  brown: 'color-brown',
} as const;

const PREDEFINED_STYLES = {
  enhanced: {
    borderRadius: '50%',
    zIndex: 10,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    transform: 'scale(1.05)',
    transition: 'all 0.2s ease-in-out',
  },
  wordInputActive: {
    outline: '2px solid #007bff',
    outlineOffset: '2px',
    backgroundColor: '#e3f2fd',
  },
  selected: {
    backgroundColor: '#fff3cd',
    borderColor: '#ffc107',
    borderWidth: '2px',
    borderStyle: 'solid',
  },
  hovered: {
    backgroundColor: '#f8f9fa',
    transform: 'translateY(-1px)',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  },
  focused: {
    outline: '2px solid #007bff',
    outlineOffset: '1px',
  },
} as const;

// ===== 统一样式计算服务 =====

export class StyleCalculationService {
  private static instance: StyleCalculationService;
  private styleRules: Map<string, StyleRule> = new Map();
  private styleCache: Map<string, StyleCalculationResult> = new Map();
  private debugMode = false;

  private constructor() {
    this.initializeDefaultRules();
  }

  /**
   * 获取服务实例
   */
  static getInstance(): StyleCalculationService {
    if (!StyleCalculationService.instance) {
      StyleCalculationService.instance = new StyleCalculationService();
    }
    return StyleCalculationService.instance;
  }

  /**
   * 设置调试模式
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  /**
   * 初始化默认样式规则
   */
  private initializeDefaultRules(): void {
    // 增强模式规则
    this.addRule({
      name: 'enhanced',
      priority: 100,
      enabled: true,
      condition: (options) => !!options.isEnhanced,
      applyStyle: () => PREDEFINED_STYLES.enhanced,
      applyClassName: (options) => {
        const baseClass = this.getBaseClassName(options);
        return options.useBEM !== false
          ? [`${baseClass}--${BEM_MODIFIERS.enhanced}`]
          : ['enhanced'];
      }
    });

    // 填词模式活跃规则
    this.addRule({
      name: 'word-input-active',
      priority: 50,
      enabled: true,
      condition: (options) => !!options.isWordInputActive,
      applyStyle: () => PREDEFINED_STYLES.wordInputActive,
      applyClassName: (options) => {
        const baseClass = this.getBaseClassName(options);
        return options.useBEM !== false
          ? [`${baseClass}--${BEM_MODIFIERS.wordInputActive}`]
          : ['word-input-active'];
      }
    });

    // 选中状态规则
    this.addRule({
      name: 'selected',
      priority: 30,
      enabled: true,
      condition: (options) => !!options.cellData?.isSelected,
      applyStyle: () => PREDEFINED_STYLES.selected,
      applyClassName: (options) => {
        const baseClass = this.getBaseClassName(options);
        return options.useBEM !== false
          ? [`${baseClass}--${BEM_MODIFIERS.selected}`]
          : ['selected'];
      }
    });

    // 悬停状态规则
    this.addRule({
      name: 'hovered',
      priority: 10,
      enabled: true,
      condition: (options) => !!options.cellData?.isHovered,
      applyStyle: () => PREDEFINED_STYLES.hovered,
      applyClassName: (options) => {
        const baseClass = this.getBaseClassName(options);
        return options.useBEM !== false
          ? [`${baseClass}--${BEM_MODIFIERS.hovered}`]
          : ['hovered'];
      }
    });

    // 焦点状态规则
    this.addRule({
      name: 'focused',
      priority: 20,
      enabled: true,
      condition: (options) => !!options.cellData?.isFocused,
      applyStyle: () => PREDEFINED_STYLES.focused,
      applyClassName: (options) => {
        const baseClass = this.getBaseClassName(options);
        return options.useBEM !== false
          ? [`${baseClass}--${BEM_MODIFIERS.focused}`]
          : ['focused'];
      }
    });

    // 级别规则
    this.addRule({
      name: 'level',
      priority: 5,
      enabled: true,
      condition: (options) => !!options.cellData?.level,
      applyStyle: () => ({}),
      applyClassName: (options) => {
        const level = options.cellData?.level;
        if (!level) return [];

        const baseClass = this.getBaseClassName(options);
        const levelModifier = `level${level}` as keyof typeof BEM_MODIFIERS;

        return options.useBEM !== false && BEM_MODIFIERS[levelModifier]
          ? [`${baseClass}--${BEM_MODIFIERS[levelModifier]}`]
          : [`level-${level}`];
      }
    });

    // 颜色规则
    this.addRule({
      name: 'color',
      priority: 5,
      enabled: true,
      condition: (options) => !!options.cellData?.color,
      applyStyle: () => ({}),
      applyClassName: (options) => {
        const color = options.cellData?.color;
        if (!color || !COLOR_CLASSES[color as keyof typeof COLOR_CLASSES]) return [];

        const baseClass = this.getBaseClassName(options);
        const colorClass = COLOR_CLASSES[color as keyof typeof COLOR_CLASSES];

        return options.useBEM !== false
          ? [`${baseClass}--${colorClass}`]
          : [`color-${color}`];
      }
    });
  }

  /**
   * 添加样式规则
   */
  addRule(rule: StyleRule): void {
    this.styleRules.set(rule.name, rule);
    this.clearCache(); // 清除缓存以应用新规则
  }

  /**
   * 移除样式规则
   */
  removeRule(ruleName: string): boolean {
    const removed = this.styleRules.delete(ruleName);
    if (removed) {
      this.clearCache();
    }
    return removed;
  }

  /**
   * 启用/禁用样式规则
   */
  toggleRule(ruleName: string, enabled: boolean): boolean {
    const rule = this.styleRules.get(ruleName);
    if (rule) {
      rule.enabled = enabled;
      this.clearCache();
      return true;
    }
    return false;
  }

  /**
   * 获取基础类名
   */
  private getBaseClassName(options: StyleCalculationOptions): string {
    return options.renderData?.className || DEFAULT_BASE_CLASS;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(options: StyleCalculationOptions): string {
    const {
      coordinate,
      cellData,
      renderData,
      config,
      isEnhanced,
      isWordInputActive,
      customClassName,
      conditionalClasses,
      useBEM,
      theme
    } = options;

    return JSON.stringify({
      coord: `${coordinate.x},${coordinate.y}`,
      selected: cellData?.isSelected,
      hovered: cellData?.isHovered,
      focused: cellData?.isFocused,
      level: cellData?.level,
      color: cellData?.color,
      mode: config?.mainMode,
      enhanced: isEnhanced,
      wordActive: isWordInputActive,
      customClass: customClassName,
      conditional: conditionalClasses,
      bem: useBEM,
      renderClass: renderData?.className,
      themeHash: theme ? JSON.stringify(theme) : null
    });
  }

  /**
   * 计算完整样式和类名
   */
  calculateStyle(options: StyleCalculationOptions): StyleCalculationResult {
    const cacheKey = this.generateCacheKey(options);

    // 检查缓存
    if (this.styleCache.has(cacheKey)) {
      return this.styleCache.get(cacheKey)!;
    }

    const {
      coordinate,
      cellData,
      renderData,
      config,
      isEnhanced = false,
      isWordInputActive = false,
      customStyle = {},
      customClassName,
      conditionalClasses = {},
      useBEM = true,
      theme = DEFAULT_THEME
    } = options;

    // 基础样式和类名
    const baseStyle = getCellStyle(isEnhanced, coordinate);
    const baseClass = this.getBaseClassName(options);

    const classArray: string[] = [baseClass];
    const modifiers: string[] = [];
    const stateClasses: string[] = [];
    let mergedStyle: React.CSSProperties = { ...baseStyle };
    let priority = 0;
    let isSpecial = false;

    // 主题样式
    const themeStyle = {
      backgroundColor: theme.backgroundColor,
      color: theme.textColor,
      borderColor: theme.borderColor,
    };
    mergedStyle = { ...mergedStyle, ...themeStyle };

    // 渲染数据样式
    if (renderData?.style) {
      mergedStyle = { ...mergedStyle, ...renderData.style };
    }

    // 应用样式规则
    const enabledRules = Array.from(this.styleRules.values())
      .filter(rule => rule.enabled)
      .sort((a, b) => b.priority - a.priority); // 高优先级先执行

    for (const rule of enabledRules) {
      try {
        if (rule.condition(options)) {
          // 应用样式
          const ruleStyle = rule.applyStyle(options);
          mergedStyle = { ...mergedStyle, ...ruleStyle };

          // 应用类名
          const ruleClasses = rule.applyClassName(options);
          classArray.push(...ruleClasses);

          // 更新状态
          priority += rule.priority;
          if (rule.priority >= 50) {
            isSpecial = true;
          }

          // 记录修饰符和状态类名
          if (useBEM && ruleClasses.length > 0) {
            const modifier = ruleClasses[0].split('--')[1];
            if (modifier) {
              modifiers.push(modifier);
            }
          }

          stateClasses.push(rule.name);

          if (this.debugMode) {
            console.log(`[StyleCalculation] 应用规则: ${rule.name}`, {
              style: ruleStyle,
              classes: ruleClasses
            });
          }
        }
      } catch (error) {
        if (this.debugMode) {
          console.error(`[StyleCalculation] 规则执行失败: ${rule.name}`, error);
        }
      }
    }

    // 配置相关类名
    if (config?.mainMode && useBEM) {
      const modeModifier = `${config.mainMode}Mode` as keyof typeof BEM_MODIFIERS;
      if (BEM_MODIFIERS[modeModifier]) {
        const modeClass = `${baseClass}--${BEM_MODIFIERS[modeModifier]}`;
        classArray.push(modeClass);
        modifiers.push(BEM_MODIFIERS[modeModifier]);
      }
    }

    // 条件类名
    Object.entries(conditionalClasses).forEach(([className, condition]) => {
      if (condition) {
        classArray.push(className);
      }
    });

    // 自定义类名
    if (customClassName) {
      classArray.push(customClassName);
    }

    // 自定义样式（最高优先级）
    mergedStyle = { ...mergedStyle, ...customStyle };

    // 去重并过滤空值
    const uniqueClasses = Array.from(new Set(classArray.filter(Boolean)));

    const result: StyleCalculationResult = {
      style: mergedStyle,
      className: uniqueClasses.join(' '),
      classArray: uniqueClasses,
      modifiers,
      stateClasses,
      isSpecial,
      priority
    };

    // 缓存结果
    this.styleCache.set(cacheKey, result);

    if (this.debugMode) {
      console.log('[StyleCalculation] 计算完成:', {
        coordinate,
        result
      });
    }

    return result;
  }

  /**
   * 仅计算类名
   */
  calculateClassName(options: StyleCalculationOptions): string {
    const result = this.calculateStyle(options);
    return result.className;
  }

  /**
   * 仅计算样式
   */
  calculateStyleOnly(options: StyleCalculationOptions): React.CSSProperties {
    const result = this.calculateStyle(options);
    return result.style;
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of this.styleCache.keys()) {
        if (key.includes(pattern)) {
          this.styleCache.delete(key);
        }
      }
    } else {
      this.styleCache.clear();
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.styleCache.size,
      keys: Array.from(this.styleCache.keys())
    };
  }

  /**
   * 批量计算样式
   */
  calculateBatchStyles(
    optionsList: StyleCalculationOptions[]
  ): StyleCalculationResult[] {
    return optionsList.map(options => this.calculateStyle(options));
  }

  /**
   * 预热缓存
   */
  preloadCache(optionsList: StyleCalculationOptions[]): void {
    optionsList.forEach(options => {
      this.calculateStyle(options);
    });
  }
}

// ===== 便捷方法 =====

/**
 * 获取样式计算服务实例
 */
export const getStyleService = () => StyleCalculationService.getInstance();

/**
 * 快速计算样式和类名
 */
export const calculateCellStyle = (options: StyleCalculationOptions) =>
  getStyleService().calculateStyle(options);

/**
 * 快速计算类名
 */
export const calculateCellClassName = (options: StyleCalculationOptions) =>
  getStyleService().calculateClassName(options);

/**
 * 快速计算样式
 */
export const calculateCellStyleOnly = (options: StyleCalculationOptions) =>
  getStyleService().calculateStyleOnly(options);

// ===== 工具函数 =====

/**
 * 样式优先级比较函数
 */
export const compareStylePriority = (
  a: StyleCalculationResult,
  b: StyleCalculationResult
): number => {
  return b.priority - a.priority;
};

/**
 * 样式合并工具
 */
export const mergeStyles = (
  ...styles: Array<React.CSSProperties | undefined>
): React.CSSProperties => {
  return styles.reduce((merged, style) => {
    if (style) {
      return { ...merged, ...style };
    }
    return merged;
  }, {});
};

/**
 * CSS类名构建工具
 */
export const buildClassName = (
  baseClass: string,
  modifiers: Record<string, boolean>
): string => {
  const classes = [baseClass];

  Object.entries(modifiers).forEach(([modifier, condition]) => {
    if (condition) {
      classes.push(`${baseClass}--${modifier}`);
    }
  });

  return classes.join(' ');
};

/**
 * BEM类名生成器
 */
export const createBEMClassName = (
  block: string,
  element?: string,
  modifiers: string[] = []
): string => {
  let baseClass = block;

  if (element) {
    baseClass += `__${element}`;
  }

  const classes = [baseClass];

  modifiers.forEach(modifier => {
    classes.push(`${baseClass}--${modifier}`);
  });

  return classes.join(' ');
};

/**
 * 条件类名生成器
 */
export const createConditionalClassName = (
  baseClass: string,
  conditions: Record<string, boolean>
): string => {
  const classes = [baseClass];

  Object.entries(conditions).forEach(([className, condition]) => {
    if (condition) {
      classes.push(className);
    }
  });

  return classes.join(' ');
};

/**
 * 主题样式生成器
 */
export const createThemeStyles = (theme: StyleTheme): React.CSSProperties => {
  return {
    '--cell-primary-color': theme.primaryColor,
    '--cell-secondary-color': theme.secondaryColor,
    '--cell-background-color': theme.backgroundColor,
    '--cell-text-color': theme.textColor,
    '--cell-border-color': theme.borderColor,
  } as React.CSSProperties;
};

/**
 * 响应式样式生成器
 */
export const createResponsiveStyles = (
  baseStyle: React.CSSProperties,
  breakpoint: 'sm' | 'md' | 'lg' | 'xl'
): React.CSSProperties => {
  const responsiveStyle = { ...baseStyle };

  switch (breakpoint) {
    case 'sm':
      responsiveStyle.fontSize = '0.75rem';
      responsiveStyle.padding = '2px';
      break;
    case 'md':
      responsiveStyle.fontSize = '0.875rem';
      responsiveStyle.padding = '4px';
      break;
    case 'lg':
      responsiveStyle.fontSize = '1rem';
      responsiveStyle.padding = '6px';
      break;
    case 'xl':
      responsiveStyle.fontSize = '1.125rem';
      responsiveStyle.padding = '8px';
      break;
  }

  return responsiveStyle;
};

// ===== 导出类型 =====
export type {
  StyleCalculationOptions,
  StyleCalculationResult,
  StyleRule,
  StyleTheme
};
