/**
 * 词库管理状态存储 - 重构版本
 * 🎯 核心价值：专注词库状态管理，集成验证服务，简化业务逻辑
 * 📦 功能范围：词库状态、词语管理、数据持久化
 * 🔄 架构设计：基于Zustand的响应式状态管理，集成独立服务
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import type {
  BasicColorType,
  DataLevel,
  WordEntry,
  WordInputState,
  WordLibrary,
  WordLibraryKey,
  WordLibraryState,
  WordValidationResult
} from '../matrix/MatrixTypes';

import {
  createInitialWordLibraryState,
  createWordEntry,
  updateDuplicateWordsMap
} from './WordLibraryCore';

import { createWordLibraryKey } from '../matrix/MatrixTypes';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== Store 接口定义 =====

interface WordLibraryStore extends WordLibraryState {
  // ===== 词库管理 =====

  /** 添加词语到指定词库 */
  addWord: (libraryKey: WordLibraryKey, text: string) => WordValidationResult;

  /** 从词库中删除词语 */
  removeWord: (libraryKey: WordLibraryKey, wordId: string) => boolean;

  /** 更新词语文本 */
  updateWord: (libraryKey: WordLibraryKey, wordId: string, newText: string) => WordValidationResult;

  /** 批量添加词语 */
  addWords: (libraryKey: WordLibraryKey, texts: string[]) => WordValidationResult[];

  /** 清空词库 */
  clearLibrary: (libraryKey: WordLibraryKey) => void;

  /** 切换词库折叠状态 */
  toggleLibraryCollapse: (libraryKey: WordLibraryKey) => void;

  /** 设置活跃词库 */
  setActiveLibrary: (libraryKey: WordLibraryKey | null) => void;

  // ===== 词语查询 =====

  /** 获取词库 */
  getLibrary: (libraryKey: WordLibraryKey) => WordLibrary | undefined;

  /** 获取词语 */
  getWord: (libraryKey: WordLibraryKey, wordId: string) => WordEntry | undefined;

  /** 搜索词语 */
  searchWords: (query: string) => Array<{ word: WordEntry; libraryKey: WordLibraryKey }>;

  /** 获取匹配的词库（根据颜色和级别） */
  getMatchingLibrary: (color: BasicColorType, level: DataLevel) => WordLibrary | undefined;

  // ===== 验证和检测 =====

  /** 验证词语 */
  validateWordText: (libraryKey: WordLibraryKey, text: string) => WordValidationResult;

  /** 输入验证（阻止性）- 用于UI层验证 */
  validateInput: (libraryKey: WordLibraryKey, text: string) => { isValid: boolean; errors: string[] };

  /** 检测跨词库重复（提醒性） */
  checkCrossLibraryDuplicate: (text: string) => { isDuplicate: boolean; duplicateLibraries: WordLibraryKey[] };

  /** 更新重复词语映射 */
  updateDuplicateMap: () => void;

  /** 获取重复词语信息 */
  getDuplicateInfo: (text: string) => WordLibraryKey[] | undefined;

  /** 获取词语高亮颜色 */
  getWordHighlightColor: (word: string) => string | undefined;

  /** 设置词语高亮颜色 */
  setWordHighlightColor: (word: string, color: string) => void;

  /** 清除词语高亮颜色 */
  clearWordHighlightColor: (word: string) => void;

  // ===== 数据管理 =====

  /** 导出词库数据 */
  exportData: () => string;

  /** 导入词库数据 */
  importData: (data: string) => boolean;

  /** 重置所有词库 */
  resetAllLibraries: () => void;

  /** 获取统计信息 */
  getStatistics: () => {
    totalLibraries: number;
    totalWords: number;
    duplicateWords: number;
    libraryStats: Array<{ libraryKey: WordLibraryKey; wordCount: number }>;
  };
}

// ===== 填词模式状态管理 =====

interface WordInputStore extends WordInputState {
  /** 激活填词模式 */
  activateWordInput: (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => Promise<void>;

  /** 退出填词模式 */
  deactivateWordInput: () => void;

  /** 选择下一个词语 */
  selectNextWord: () => void;

  /** 选择上一个词语 */
  selectPreviousWord: () => void;

  /** 确认选择当前词语 */
  confirmWordSelection: () => WordEntry | null;

  /** 删除当前单元格的词语 */
  clearCellWord: () => void;

  /** 检查词库是否为空并显示提醒 */
  checkLibraryEmpty: () => boolean;
}

// ===== 选择器函数 =====

/**
 * 获取特定词库的选择器
 */
export const selectLibrary = (libraryKey: WordLibraryKey) => (state: WordLibraryState) =>
  state.libraries.get(libraryKey);

/**
 * 获取词库折叠状态的选择器
 */
export const selectLibraryCollapsed = (libraryKey: WordLibraryKey) => (state: WordLibraryState) =>
  state.libraries.get(libraryKey)?.collapsed ?? false;

/**
 * 获取词库词语列表的选择器
 */
export const selectLibraryWords = (libraryKey: WordLibraryKey) => (state: WordLibraryState) =>
  state.libraries.get(libraryKey)?.words ?? [];

/**
 * 获取词库词语数量的选择器
 */
export const selectLibraryWordCount = (libraryKey: WordLibraryKey) => (state: WordLibraryState) =>
  state.libraries.get(libraryKey)?.words.length ?? 0;

/**
 * 获取活跃词库的选择器
 */
export const selectActiveLibrary = (state: WordLibraryState) => state.activeLibrary;

/**
 * 获取重复词语映射的选择器
 */
export const selectDuplicateWords = (state: WordLibraryState) => state.duplicateWords;

// ===== 填词模式选择器 =====

/**
 * 获取填词模式激活状态的选择器
 */
export const selectWordInputActive = (state: WordInputState) => state.isActive;

/**
 * 获取匹配词库的选择器
 */
export const selectMatchedLibrary = (state: WordInputState) => state.matchedLibrary;

/**
 * 获取临时词语的选择器
 */
export const selectTemporaryWord = (state: WordInputState) => state.temporaryWord;

/**
 * 获取选中单元格的选择器
 */
export const selectSelectedCell = (state: WordInputState) => state.selectedCell;

/**
 * 检查特定词库是否激活的选择器
 */
export const selectIsLibraryActive = (libraryKey: WordLibraryKey) => (state: WordInputState) =>
  state.isActive && state.matchedLibrary === libraryKey;

// ===== 创建词库管理Store =====

export const useWordLibraryStore = create<WordLibraryStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      ...createInitialWordLibraryState(),

      // ===== 词库管理方法 =====

      addWord: (libraryKey: WordLibraryKey, text: string) => {
        // 使用新的输入验证（阻止性）
        const inputValidation = get().validateInput(libraryKey, text);

        if (!inputValidation.isValid) {
          // 输入验证失败，直接返回错误，不录入数据
          return {
            isValid: false,
            errors: inputValidation.errors,
            isDuplicate: false,
            duplicateLibraries: []
          };
        }

        // 输入验证通过，直接录入数据
        const trimmedText = text.trim();
        let addedSuccessfully = false;

        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            const wordEntry = createWordEntry(trimmedText, library.color, library.level);
            library.words.push(wordEntry);
            library.lastUpdated = new Date();

            // 同步更新全局索引
            const { addWordToGlobalIndex } = require('./WordLibraryCore');
            addWordToGlobalIndex(draft.globalWordIndex, trimmedText, libraryKey);

            addedSuccessfully = true;
          }
        }));

        if (addedSuccessfully) {
          // 检查跨词库重复并更新高亮
          const crossLibraryCheck = get().checkCrossLibraryDuplicate(trimmedText);

          if (crossLibraryCheck.isDuplicate) {
            // 为跨词库重复词语分配高亮颜色
            const currentColor = get().getWordHighlightColor(trimmedText);
            if (!currentColor) {
              const { assignWordHighlightColor } = require('./WordLibraryCore');
              const newColor = assignWordHighlightColor(get().usedHighlightColors);
              get().setWordHighlightColor(trimmedText, newColor);
            }
          }

          // 更新重复词语映射
          get().updateDuplicateMap();

          return {
            isValid: true,
            errors: [],
            isDuplicate: crossLibraryCheck.isDuplicate,
            duplicateLibraries: crossLibraryCheck.duplicateLibraries
          };
        }

        return {
          isValid: false,
          errors: ['添加词语失败'],
          isDuplicate: false,
          duplicateLibraries: []
        };
      },

      removeWord: (libraryKey: WordLibraryKey, wordId: string) => {
        let removed = false;
        let removedWordText = '';

        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            const wordIndex = library.words.findIndex((word: WordEntry) => word.id === wordId);
            if (wordIndex !== -1) {
              removedWordText = library.words[wordIndex].text;
              library.words.splice(wordIndex, 1);
              library.lastUpdated = new Date();

              // 同步更新全局索引
              const { removeWordFromGlobalIndex } = require('./WordLibraryCore');
              removeWordFromGlobalIndex(draft.globalWordIndex, removedWordText, libraryKey);

              removed = true;
            }
          }
        }));

        if (removed) {
          // 检查是否还有跨词库重复，如果没有则清除高亮颜色
          const crossLibraryCheck = get().checkCrossLibraryDuplicate(removedWordText);
          if (!crossLibraryCheck.isDuplicate) {
            get().clearWordHighlightColor(removedWordText);
          }

          get().updateDuplicateMap();
        }

        return removed;
      },

      updateWord: (libraryKey: WordLibraryKey, wordId: string, newText: string) => {
        const state = get();
        const validation = validateWord(newText, libraryKey, state.libraries);

        if (validation.isValid) {
          set(produce((draft) => {
            const library = draft.libraries.get(libraryKey);
            if (library) {
              const word = library.words.find((w: WordEntry) => w.id === wordId);
              if (word) {
                word.text = newText.trim();
                word.updatedAt = new Date();
                library.lastUpdated = new Date();
              }
            }
          }));

          get().updateDuplicateMap();
        }

        return validation;
      },

      addWords: (libraryKey: WordLibraryKey, texts: string[]) => {
        const results: WordValidationResult[] = [];

        texts.forEach(text => {
          const result = get().addWord(libraryKey, text);
          results.push(result);
        });

        return results;
      },

      clearLibrary: (libraryKey: WordLibraryKey) => {
        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            library.words = [];
            library.lastUpdated = new Date();
          }
        }));

        get().updateDuplicateMap();

        // 清理相关的单元格词语绑定
        try {
          const { useMatrixStore } = require('../matrix/MatrixStore');
          const matrixStore = useMatrixStore.getState();
          matrixStore.cleanupInvalidWordBindings();
        } catch (error) {
          console.warn('清理单元格绑定时出错:', error);
        }
      },

      toggleLibraryCollapse: (libraryKey: WordLibraryKey) => {
        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            library.collapsed = !library.collapsed;
          }
        }));
      },

      setActiveLibrary: (libraryKey: WordLibraryKey | null) => {
        set({ activeLibrary: libraryKey });
      },

      // ===== 查询方法 =====

      getLibrary: (libraryKey: WordLibraryKey) => {
        const state = get();
        // 安全检查：确保libraries是Map对象
        if (!(state.libraries instanceof Map)) {
          console.warn('⚠️ libraries不是Map对象，正在重新初始化...');
          // 重新初始化状态
          const defaultState = createInitialWordLibraryState();
          set(defaultState);
          return defaultState.libraries.get(libraryKey);
        }
        return state.libraries.get(libraryKey);
      },

      getWord: (libraryKey: WordLibraryKey, wordId: string) => {
        const library = get().libraries.get(libraryKey);
        return library?.words.find(word => word.id === wordId);
      },

      searchWords: (query: string) => {
        const results: Array<{ word: WordEntry; libraryKey: WordLibraryKey }> = [];
        const state = get();

        state.libraries.forEach((library, libraryKey) => {
          library.words.forEach(word => {
            if (word.text.includes(query)) {
              results.push({ word, libraryKey });
            }
          });
        });

        return results;
      },

      getMatchingLibrary: (color: BasicColorType, level: DataLevel) => {
        const libraryKey = createWordLibraryKey(color, level);
        return get().libraries.get(libraryKey);
      },

      // ===== 验证方法 =====

      validateWordText: (libraryKey: WordLibraryKey, text: string) => {
        const state = get();
        return validateWord(text, libraryKey, state.libraries);
      },

      validateInput: (libraryKey: WordLibraryKey, text: string) => {
        const state = get();
        // 导入验证函数
        const { validateInputBeforeSubmit } = require('./WordLibraryCore');
        return validateInputBeforeSubmit(text, libraryKey, state.globalWordIndex);
      },

      checkCrossLibraryDuplicate: (text: string) => {
        const state = get();
        // 导入检测函数
        const { detectCrossLibraryDuplicates } = require('./WordLibraryCore');
        return detectCrossLibraryDuplicates(text, state.globalWordIndex);
      },

      updateDuplicateMap: () => {
        const state = get();

        // 使用增强版函数同时更新重复词语映射和高亮颜色
        const { updateDuplicateWordsMapWithColors } = require('./WordLibraryCore');
        const result = updateDuplicateWordsMapWithColors(
          state.libraries,
          state.wordHighlightColors,
          state.usedHighlightColors
        );

        set({
          duplicateWords: result.duplicateWords,
          wordHighlightColors: result.wordHighlightColors,
          usedHighlightColors: result.usedHighlightColors
        });
      },

      getDuplicateInfo: (text: string) => {
        return get().duplicateWords.get(text);
      },

      // ===== 数据管理方法 =====

      exportData: () => {
        const state = get();
        const exportData = {
          libraries: Array.from(state.libraries.entries()),
          lastSyncTime: state.lastSyncTime,
          exportTime: new Date()
        };
        return JSON.stringify(exportData, null, 2);
      },

      importData: (data: string) => {
        try {
          const importData = JSON.parse(data);
          const libraries = new Map(importData.libraries) as Map<WordLibraryKey, WordLibrary>;

          set({
            libraries,
            lastSyncTime: new Date(),
            duplicateWords: updateDuplicateWordsMap(libraries)
          });

          return true;
        } catch (error) {
          console.error('导入数据失败:', error);
          return false;
        }
      },

      resetAllLibraries: () => {
        set(createInitialWordLibraryState());

        // 清理所有单元格词语绑定
        try {
          const { useMatrixStore } = require('../matrix/MatrixStore');
          const matrixStore = useMatrixStore.getState();
          matrixStore.clearAllWordBindings();
        } catch (error) {
          console.warn('清理单元格绑定时出错:', error);
        }
      },

      // ===== 随机颜色管理方法 =====

      getWordHighlightColor: (word: string) => {
        const state = get();
        return state.wordHighlightColors.get(word);
      },

      setWordHighlightColor: (word: string, color: string) => {
        set(produce((draft) => {
          draft.wordHighlightColors.set(word, color);
          draft.usedHighlightColors.add(color);
        }));
      },

      clearWordHighlightColor: (word: string) => {
        set(produce((draft) => {
          const color = draft.wordHighlightColors.get(word);
          if (color) {
            draft.wordHighlightColors.delete(word);
            // 检查是否还有其他词语使用这个颜色
            const isColorStillUsed = Array.from(draft.wordHighlightColors.values()).includes(color);
            if (!isColorStillUsed) {
              draft.usedHighlightColors.delete(color);
            }
          }
        }));
      },

      getStatistics: () => {
        const state = get();
        const libraryStats: Array<{ libraryKey: WordLibraryKey; wordCount: number }> = [];
        let totalWords = 0;

        state.libraries.forEach((library, libraryKey) => {
          const wordCount = library.words.length;
          libraryStats.push({ libraryKey, wordCount });
          totalWords += wordCount;
        });

        return {
          totalLibraries: state.libraries.size,
          totalWords,
          duplicateWords: state.duplicateWords.size,
          libraryStats
        };
      }
    }),
    {
      name: 'word-library-storage',
      version: 2, // 增加版本号以触发数据迁移
      partialize: (state) => ({
        libraries: Array.from(state.libraries.entries()),
        activeLibrary: state.activeLibrary,
        duplicateWords: Array.from(state.duplicateWords.entries()),
        wordHighlightColors: Array.from(state.wordHighlightColors.entries()),
        usedHighlightColors: Array.from(state.usedHighlightColors),
        globalWordIndex: Array.from(state.globalWordIndex.entries()).map(([word, libraries]) => [word, Array.from(libraries)]),
        isLoading: state.isLoading,
        lastSyncTime: state.lastSyncTime
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          try {
            // 处理libraries字段
            if (Array.isArray(state.libraries)) {
              // 新格式：数组格式
              state.libraries = new Map(state.libraries as any);
            } else if (state.libraries && typeof state.libraries === 'object' && !(state.libraries instanceof Map)) {
              // 旧格式：普通对象格式，需要转换
              console.log('🔄 检测到旧版本数据格式，正在迁移...');
              const entries = Object.entries(state.libraries);
              state.libraries = new Map(entries as any);
            } else if (!state.libraries) {
              // 没有数据，使用默认值
              state.libraries = new Map();
            }

            // 处理duplicateWords字段
            if (Array.isArray(state.duplicateWords)) {
              state.duplicateWords = new Map(state.duplicateWords as any);
            } else if (state.duplicateWords && typeof state.duplicateWords === 'object' && !(state.duplicateWords instanceof Map)) {
              const entries = Object.entries(state.duplicateWords);
              state.duplicateWords = new Map(entries as any);
            } else if (!state.duplicateWords) {
              state.duplicateWords = new Map();
            }

            // 处理wordHighlightColors字段
            if (Array.isArray(state.wordHighlightColors)) {
              state.wordHighlightColors = new Map(state.wordHighlightColors as any);
            } else if (state.wordHighlightColors && typeof state.wordHighlightColors === 'object' && !(state.wordHighlightColors instanceof Map)) {
              const entries = Object.entries(state.wordHighlightColors);
              state.wordHighlightColors = new Map(entries as any);
            } else if (!state.wordHighlightColors) {
              state.wordHighlightColors = new Map();
            }

            // 处理usedHighlightColors字段
            if (Array.isArray(state.usedHighlightColors)) {
              state.usedHighlightColors = new Set(state.usedHighlightColors as any);
            } else if (state.usedHighlightColors && typeof state.usedHighlightColors === 'object' && !(state.usedHighlightColors instanceof Set)) {
              // 如果是对象格式，尝试获取值
              const values = Object.values(state.usedHighlightColors);
              state.usedHighlightColors = new Set(values as any);
            } else if (!state.usedHighlightColors) {
              state.usedHighlightColors = new Set();
            }

            // 处理globalWordIndex字段
            if (Array.isArray(state.globalWordIndex)) {
              // 新格式：数组格式 [[word, [library1, library2]], ...]
              state.globalWordIndex = new Map(
                (state.globalWordIndex as any).map(([word, libraries]: [string, string[]]) => [
                  word,
                  new Set(libraries)
                ])
              );
            } else if (!state.globalWordIndex) {
              // 没有索引数据，从现有词库重建
              console.log('🔄 重建全局词语索引...');
              const { buildGlobalWordIndex } = require('./WordLibraryCore');
              state.globalWordIndex = buildGlobalWordIndex(state.libraries);
            }

            console.log('✅ 数据迁移完成');
          } catch (error) {
            console.error('❌ 数据迁移失败，使用默认状态:', error);
            // 如果迁移失败，重置为默认状态
            const defaultState = createInitialWordLibraryState();
            Object.assign(state, defaultState);
          }
        }
      }
    }
  )
);

// ===== 创建填词模式Store =====

export const useWordInputStore = create<WordInputStore>((set, get) => ({
  // 初始状态
  isActive: false,
  selectedCell: null,
  matchedLibrary: null,
  selectedWordIndex: 0,
  availableWords: [],
  temporaryWord: null,
  isWordBound: false,

  activateWordInput: async (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => {
    const libraryKey = createWordLibraryKey(color, level);
    const wordLibraryStore = useWordLibraryStore.getState();
    const library = wordLibraryStore.getLibrary(libraryKey);

    let selectedIndex = 0;
    let temporaryWord = library?.words?.[0]?.text || null;
    let isWordBound = false;

    // 如果传递了绑定的词语ID，定位到该词语
    if (boundWordId && library?.words) {
      const boundWordIndex = library.words.findIndex(word => word.id === boundWordId);
      if (boundWordIndex !== -1) {
        selectedIndex = boundWordIndex;
        temporaryWord = library.words[boundWordIndex].text;
        isWordBound = true;
      }
    }

    set({
      isActive: true,
      selectedCell: { x, y },
      matchedLibrary: libraryKey,
      selectedWordIndex: selectedIndex,
      availableWords: library?.words || [],
      temporaryWord,
      isWordBound
    });
  },

  deactivateWordInput: () => {
    set({
      isActive: false,
      selectedCell: null,
      matchedLibrary: null,
      selectedWordIndex: 0,
      availableWords: [],
      temporaryWord: null,
      isWordBound: false
    });
  },

  selectNextWord: () => {
    const state = get();
    if (state.availableWords.length > 0) {
      const nextIndex = (state.selectedWordIndex + 1) % state.availableWords.length;
      const nextWord = state.availableWords[nextIndex];
      set({
        selectedWordIndex: nextIndex,
        temporaryWord: nextWord?.text || null
      });
    }
  },

  selectPreviousWord: () => {
    const state = get();
    if (state.availableWords.length > 0) {
      const prevIndex = state.selectedWordIndex === 0
        ? state.availableWords.length - 1
        : state.selectedWordIndex - 1;
      const prevWord = state.availableWords[prevIndex];
      set({
        selectedWordIndex: prevIndex,
        temporaryWord: prevWord?.text || null
      });
    }
  },

  confirmWordSelection: () => {
    const state = get();
    if (state.availableWords.length > 0 && state.selectedWordIndex < state.availableWords.length) {
      const selectedWord = state.availableWords[state.selectedWordIndex];

      // 更新词语使用统计
      if (state.matchedLibrary && state.selectedCell) {
        // 这里可以添加使用统计更新逻辑
        // const wordLibraryStore = useWordLibraryStore.getState();
      }

      // 标记为已绑定
      set({ isWordBound: true });
      return selectedWord;
    }
    return null;
  },

  clearCellWord: () => {
    // 清除单元格词语的逻辑
    // 这里需要与矩阵状态管理集成
  },

  checkLibraryEmpty: () => {
    const state = get();
    if (!state.matchedLibrary) return true;

    const wordLibraryStore = useWordLibraryStore.getState();
    const library = wordLibraryStore.getLibrary(state.matchedLibrary);

    return !library || library.words.length === 0;
  }
}));
