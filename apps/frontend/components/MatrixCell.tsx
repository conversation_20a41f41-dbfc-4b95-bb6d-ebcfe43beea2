/**
 * 矩阵单元格组件
 * 🎯 核心价值：专门负责单个单元格的渲染，职责单一，高度可复用
 * 📦 功能范围：单元格渲染、样式计算、交互事件处理
 * 🔄 架构设计：纯组件，通过props接收所有数据和回调
 */

'use client';

import { CellStateCalculator } from '@/core/matrix/CellStateCalculator';
import type { CellData, CellRenderData, MatrixConfig } from '@/core/matrix/MatrixTypes';
import { useUnifiedCellStyle } from '@/hooks/useUnifiedCellStyle';
import { useMatrixCellEventHandler } from '@/hooks/useUnifiedEventHandler';
import React, { memo, useMemo } from 'react';

// ===== 组件属性接口 =====

interface MatrixCellProps {
  /** 单元格坐标 */
  x: number;
  y: number;

  /** 单元格数据 */
  cellData?: CellData;

  /** 渲染数据 */
  renderData?: CellRenderData;

  /** 矩阵配置 */
  config: MatrixConfig;

  /** 是否为增强模式（颜色模式下的1级坐标） */
  isEnhanced?: boolean;

  /** 是否为填词模式下的活跃单元格 */
  isWordInputActive?: boolean;

  /** 临时显示的词语（填词模式） */
  temporaryWord?: string;

  /** 自定义样式 */
  style?: React.CSSProperties;

  /** 自定义CSS类名 */
  className?: string;

  /** 交互事件回调 */
  onClick?: (x: number, y: number, event: React.MouseEvent) => void;
  onDoubleClick?: (x: number, y: number, event: React.MouseEvent) => void;
  onMouseEnter?: (x: number, y: number, event: React.MouseEvent) => void;
  onMouseLeave?: (x: number, y: number, event: React.MouseEvent) => void;
  onFocus?: (x: number, y: number, event: React.FocusEvent) => void;
  onBlur?: (x: number, y: number, event: React.FocusEvent) => void;
}

// ===== 样式计算辅助函数 =====

/**
 * 计算显示内容
 */
const calculateDisplayContent = (
  renderData?: CellRenderData,
  temporaryWord?: string,
  isWordInputActive?: boolean,
  config?: MatrixConfig
): string => {
  // 临时词语优先（仅在颜色+词语模式下）
  if (
    isWordInputActive &&
    temporaryWord &&
    config?.mainMode === 'color' &&
    config?.contentMode === 'word'
  ) {
    return temporaryWord;
  }

  // 默认显示渲染数据内容
  return renderData?.content || '';
};

// ===== 主组件 =====

const MatrixCellComponent: React.FC<MatrixCellProps> = ({
  x,
  y,
  cellData,
  renderData,
  config,
  isEnhanced = false,
  isWordInputActive = false,
  temporaryWord,
  style: customStyle,
  className: customClassName,
  onClick,
  onDoubleClick,
  onMouseEnter,
  onMouseLeave,
  onFocus,
  onBlur,
}) => {
  // 使用统一样式Hook计算样式和类名
  const { style: cellStyle, className: cellClassName } = useUnifiedCellStyle({
    coordinate: { x, y },
    cellData,
    renderData,
    config,
    isEnhanced,
    isWordInputActive,
    customStyle,
    customClassName,
    enableCache: true,
    debug: false
  });

  // 使用状态计算器计算显示内容
  const displayResult = CellStateCalculator.calculateDisplay({
    x,
    y,
    renderData,
    config,
    wordInputState: {
      isActive: isWordInputActive,
      selectedCell: isWordInputActive ? { x, y } : null
    },
    temporaryWord,
  });

  const displayContent = displayResult.content;

  // 使用统一事件处理Hook
  const eventHandlers = useMemo(() => {
    return useMatrixCellEventHandler(x, y, {
      onClick,
      onDoubleClick,
      onMouseEnter,
      onMouseLeave,
      onFocus,
      onBlur
    }, {
      enableDebounce: false, // 单元格事件通常不需要防抖
      enableThrottle: false, // 单元格事件通常不需要节流
      debug: false
    })();
  }, [x, y, onClick, onDoubleClick, onMouseEnter, onMouseLeave, onFocus, onBlur]);

  return (
    <div
      data-x={x}
      data-y={y}
      data-cell={`${x},${y}`}
      data-color={cellData?.color}
      data-level={cellData?.level}
      className={cellClassName}
      style={cellStyle}
      onClick={eventHandlers.onClick}
      onDoubleClick={eventHandlers.onDoubleClick}
      onMouseEnter={eventHandlers.onMouseEnter}
      onMouseLeave={eventHandlers.onMouseLeave}
      onFocus={eventHandlers.onFocus}
      onBlur={eventHandlers.onBlur}
      tabIndex={0} // 支持键盘导航
      role="gridcell"
      aria-label={`单元格 ${x},${y}${displayContent ? `: ${displayContent}` : ''}`}
    >
      {displayContent}
    </div>
  );
};

// ===== 性能优化 =====

/**
 * 自定义比较函数，优化重渲染
 */
const arePropsEqual = (
  prevProps: MatrixCellProps,
  nextProps: MatrixCellProps
): boolean => {
  // 基础属性比较
  if (
    prevProps.x !== nextProps.x ||
    prevProps.y !== nextProps.y ||
    prevProps.isEnhanced !== nextProps.isEnhanced ||
    prevProps.isWordInputActive !== nextProps.isWordInputActive ||
    prevProps.temporaryWord !== nextProps.temporaryWord
  ) {
    return false;
  }

  // 渲染数据比较
  if (prevProps.renderData !== nextProps.renderData) {
    // 深度比较渲染数据的关键属性
    const prevRender = prevProps.renderData;
    const nextRender = nextProps.renderData;

    if (
      prevRender?.content !== nextRender?.content ||
      prevRender?.className !== nextRender?.className ||
      JSON.stringify(prevRender?.style) !== JSON.stringify(nextRender?.style)
    ) {
      return false;
    }
  }

  // 单元格数据比较
  if (prevProps.cellData !== nextProps.cellData) {
    const prevCell = prevProps.cellData;
    const nextCell = nextProps.cellData;

    if (
      prevCell?.level !== nextCell?.level ||
      prevCell?.color !== nextCell?.color ||
      prevCell?.isSelected !== nextCell?.isSelected ||
      prevCell?.isHovered !== nextCell?.isHovered ||
      prevCell?.isFocused !== nextCell?.isFocused
    ) {
      return false;
    }
  }

  // 配置比较（只比较影响渲染的属性）
  if (
    prevProps.config.mainMode !== nextProps.config.mainMode ||
    prevProps.config.contentMode !== nextProps.config.contentMode
  ) {
    return false;
  }

  // 样式和类名比较
  if (
    JSON.stringify(prevProps.style) !== JSON.stringify(nextProps.style) ||
    prevProps.className !== nextProps.className
  ) {
    return false;
  }

  return true;
};

// ===== 导出组件 =====

export const MatrixCell = memo(MatrixCellComponent, arePropsEqual);

// ===== 导出类型 =====

export type { MatrixCellProps };

// ===== 导出辅助函数 =====

export {
  calculateDisplayContent
};

