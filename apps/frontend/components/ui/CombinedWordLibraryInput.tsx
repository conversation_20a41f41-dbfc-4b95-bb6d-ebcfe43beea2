/**
 * 合并式词库输入组件
 * 🎯 核心价值：标题和内容合并的一体化词库输入框，支持展开/折叠和横向滚动
 * 📦 功能范围：词库标题显示、词语列表展示、输入框、展开折叠控制
 * 🔄 架构设计：一体化容器设计，与矩阵系统颜色同步
 */

'use client';

import type {
  BasicColorType,
  DataLevel,
  WordLibraryKey,
  WordValidationResult
} from '@/core/matrix/MatrixTypes';
import { toast } from '@/core/ui/ToastStore';
import {
  getWordLibraryBackgroundColor,
  getWordLibraryDisplayName,
  getWordLibraryTextColor,
  parseInputText
} from '@/core/wordLibrary/WordLibraryCore';
import { useIsolatedWordLibraryEvents } from '@/hooks/useIsolatedWordLibraryEvents';
import {
  useCurrentTemporaryWord,
  useOptimizedLibrary,
  useWordLibraryActions
} from '@/hooks/useOptimizedWordLibraryStore';
import React, { memo, useCallback, useMemo, useRef, useState } from 'react';

// ===== 组件属性 =====

interface CombinedWordLibraryInputProps {
  /** 词库标识 */
  libraryKey: WordLibraryKey;
  /** 颜色 */
  color: BasicColorType;
  /** 级别 */
  level: DataLevel;
  /** 是否折叠状态 */
  collapsed?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 自定义类名 */
  className?: string;
  /** 输入变化回调 */
  onChange?: (words: string[]) => void;
  /** 验证结果回调 */
  onValidation?: (results: WordValidationResult[]) => void;
}

// ===== 词语标签组件 =====

interface WordTagProps {
  text: string;
  usageCount: number;
  backgroundColor: string;
  textColor: string;
  isSelected?: boolean;
  isDuplicate?: boolean;
  duplicateColor?: string;
  onRemove: () => void;
}

const WordTag: React.FC<WordTagProps> = ({
  text,
  usageCount,
  backgroundColor,
  textColor,
  isSelected = false,
  isDuplicate = false,
  duplicateColor,
  onRemove
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <span
      className="inline-flex items-center relative transition-all duration-200"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        backgroundColor: isDuplicate && duplicateColor ? duplicateColor + '30' : 'transparent',
        borderRadius: '4px',
        padding: '2px 4px',
        textDecoration: isSelected ? 'underline' : 'none',
        textDecorationColor: isSelected ? textColor : 'transparent'
      }}
    >
      <span style={{ color: textColor, fontSize: '14px' }}>
        {text}
        {isHovered && (
          <span className="text-xs opacity-70">
            [{usageCount}]
          </span>
        )}
      </span>
      {isHovered && (
        <button
          onClick={onRemove}
          className="ml-1 text-red-500 hover:text-red-700 text-xs transition-colors duration-200"
          title="删除词语"
        >
          ×
        </button>
      )}
    </span>
  );
};

// ===== 主组件 =====

const CombinedWordLibraryInput: React.FC<CombinedWordLibraryInputProps> = memo(({
  libraryKey,
  color,
  level,
  collapsed = false,
  placeholder = '输入词语，用逗号分隔...',
  className = '',
  onChange,
  onValidation
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [inputValue, setInputValue] = useState('');
  const [isExpanded, setIsExpanded] = useState(!collapsed);

  // 使用优化的状态订阅
  const { library } = useOptimizedLibrary(libraryKey);
  const {
    validateInput,
    toggleLibraryCollapse,
    checkCrossLibraryDuplicate,
    getWordHighlightColor
  } = useWordLibraryActions();

  // 使用隔离的事件处理器
  const {
    handleAddWord,
    handleRemoveWord,
    handleToggleCollapse
  } = useIsolatedWordLibraryEvents({
    debounce: true,
    debounceDelay: 200,
    debug: false
  });

  // 获取填词模式状态
  const temporaryWord = useCurrentTemporaryWord();

  // 使用useMemo缓存计算结果
  const displayName = useMemo(() => getWordLibraryDisplayName(color, level), [color, level]);
  const backgroundColor = useMemo(() => getWordLibraryBackgroundColor(color), [color]);
  const textColor = useMemo(() => getWordLibraryTextColor(color), [color]);

  // 显示错误提示
  const showErrorMessage = useCallback((message: string) => {
    toast.error(message, {
      duration: 3000,
      position: 'top-right'
    });
  }, []);

  // 处理输入确认（逗号或回车）
  const handleInputConfirm = useCallback(async () => {
    if (!inputValue.trim()) return;

    const newWords = parseInputText(inputValue);
    const validWords: string[] = [];
    const errorWords: string[] = [];

    // 逐个验证和添加词语
    for (const word of newWords) {
      const validation = validateInput(libraryKey, word);
      if (validation.isValid) {
        try {
          const result = await handleAddWord(libraryKey, word);
          if (result.isValid) {
            validWords.push(word);

            // 检查是否为跨词库重复词语，显示提醒
            if (result.isDuplicate && result.duplicateLibraries && result.duplicateLibraries.length > 1) {
              const otherLibraries = result.duplicateLibraries.filter(lib => lib !== libraryKey);
              if (otherLibraries.length > 0) {
                toast.warning(`词语"${word}"在其他词库中也存在`, {
                  duration: 3000,
                  position: 'top-right'
                });
              }
            }
          }
        } catch (error) {
          console.error(`添加词语"${word}"失败:`, error);
          errorWords.push(word);
        }
      } else {
        errorWords.push(word);
        console.warn(`词语"${word}"验证失败:`, validation.errors);
      }
    }

    // 清空输入框
    setInputValue('');

    // 显示错误信息
    if (errorWords.length > 0) {
      showErrorMessage(`无效词语: ${errorWords.join(', ')}`);
    }

    // 触发回调
    if (validWords.length > 0) {
      onChange?.(validWords);
    }
  }, [inputValue, libraryKey, validateInput, handleAddWord, onChange, showErrorMessage]);

  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // 自动格式化逗号：将中文逗号转换为英文逗号，并确保逗号后有空格
    value = value.replace(/，/g, ', ').replace(/,\s*/g, ', ');

    setInputValue(value);

    // 实时检测逗号并自动确认输入
    if (value.endsWith(', ') && value.trim().length > 2) {
      // 延迟500ms确保用户看到逗号，然后自动确认
      setTimeout(() => {
        handleInputConfirm();
      }, 500);
    }
  }, [handleInputConfirm]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // Enter键直接确认输入
      handleInputConfirm();
    } else if (e.key === ',' || e.key === '，') {
      // 逗号键不阻止默认行为，让输入变化事件处理实时识别
      // 这样可以实现更灵敏的识别
    }
  }, [handleInputConfirm]);

  // 删除词语
  const handleRemoveWordClick = useCallback(async (wordId: string) => {
    if (library) {
      try {
        await handleRemoveWord(libraryKey, wordId);
      } catch (error) {
        console.error('删除词语失败:', error);
      }
    }
  }, [library, handleRemoveWord, libraryKey]);

  // 切换展开/折叠
  const handleToggleExpanded = useCallback(async () => {
    setIsExpanded(!isExpanded);
    try {
      await handleToggleCollapse(libraryKey);
    } catch (error) {
      console.error('切换折叠状态失败:', error);
    }
  }, [isExpanded, handleToggleCollapse, libraryKey]);

  if (!library) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className={`relative rounded-md border transition-all duration-200 ${className}`}
      style={{
        backgroundColor: backgroundColor + '20',
        borderColor: backgroundColor + '40',
        color: textColor
      }}
    >
      {/* 主容器 - 一体化布局 */}
      <div className="flex items-center flex-wrap gap-1 p-3">
        {/* 折叠/展开按钮 */}
        <button
          onClick={handleToggleExpanded}
          className="flex-shrink-0 w-4 h-4 flex items-center justify-center text-xs hover:bg-black hover:bg-opacity-10 rounded"
          style={{ color: textColor }}
        >
          {isExpanded ? '−' : '+'}
        </button>

        {/* 标题部分 */}
        <span className="flex-shrink-0 font-medium">
          【{displayName}[{library.words.length}词]：
        </span>

        {/* 词语列表部分 */}
        <div className={`flex items-center gap-1 ${isExpanded ? 'flex-wrap' : 'overflow-x-auto whitespace-nowrap'}`}>
          {isExpanded ? (
            // 展开模式：显示所有词语
            <>
              {library.words.map((word, index) => {
                // 检查是否为矩阵系统选中的词语
                const isSelected = temporaryWord === word.text;

                // 检查是否为跨词库重复词语
                const duplicateCheck = checkCrossLibraryDuplicate(word.text);
                const isDuplicate = duplicateCheck.isDuplicate && duplicateCheck.duplicateLibraries.length > 1;
                const duplicateColor = isDuplicate ? getWordHighlightColor(word.text) : undefined;

                return (
                  <React.Fragment key={word.id}>
                    <WordTag
                      text={word.text}
                      usageCount={word.usagePositions?.length || 0}
                      backgroundColor={backgroundColor}
                      textColor={textColor}
                      isSelected={isSelected}
                      isDuplicate={isDuplicate}
                      duplicateColor={duplicateColor}
                      onRemove={() => handleRemoveWordClick(word.id)}
                    />
                    <span style={{ color: textColor }}>，</span>
                  </React.Fragment>
                );
              })}

              {/* 输入框 */}
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleInputConfirm}
                placeholder={library.words.length === 0 ? placeholder : '继续输入...'}
                className="bg-transparent border-none outline-none min-w-[120px] flex-grow"
                style={{ color: textColor, fontSize: '14px' }}
              />
            </>
          ) : (
            // 折叠模式：显示前3个词语，其余用省略号
            <>
              {library.words.slice(0, 3).map((word, index) => {
                // 检查是否为矩阵系统选中的词语
                const isSelected = temporaryWord === word.text;

                // 检查是否为跨词库重复词语
                const duplicateCheck = checkCrossLibraryDuplicate(word.text);
                const isDuplicate = duplicateCheck.isDuplicate && duplicateCheck.duplicateLibraries.length > 1;
                const duplicateColor = isDuplicate ? getWordHighlightColor(word.text) : undefined;

                return (
                  <React.Fragment key={word.id}>
                    <span
                      style={{
                        color: textColor,
                        fontSize: '14px',
                        textDecoration: isSelected ? 'underline' : 'none',
                        backgroundColor: isDuplicate && duplicateColor ? duplicateColor + '30' : 'transparent',
                        borderRadius: '4px',
                        padding: '2px 4px'
                      }}
                    >
                      {word.text}[{word.usagePositions?.length || 0}]
                    </span>
                    {index < 2 && (
                      <span style={{ color: textColor }}>，</span>
                    )}
                  </React.Fragment>
                );
              })}
              {library.words.length > 3 && (
                <span style={{ color: textColor }}>…</span>
              )}

              {/* 折叠模式下的输入框 */}
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleInputConfirm}
                placeholder={library.words.length === 0 ? placeholder : '继续输入...'}
                className="bg-transparent border-none outline-none min-w-[120px] flex-grow"
                style={{ color: textColor, fontSize: '14px' }}
              />
            </>
          )}
        </div>

        {/* 结束标记 */}
        <span className="flex-shrink-0">】</span>
      </div>

      {/* 提示信息 */}
      {/* <div className="px-3 pb-2 text-xs opacity-70">
        {library.words.length} 个词语 | 用逗号分隔或按回车确认
      </div> */}
    </div>
  );
});

CombinedWordLibraryInput.displayName = 'CombinedWordLibraryInput';

export default CombinedWordLibraryInput;
export type { CombinedWordLibraryInputProps };
