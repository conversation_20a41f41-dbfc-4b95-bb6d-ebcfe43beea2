/**
 * 词库管理主组件
 * 🎯 核心价值：统一的词库管理界面，支持29个词库的可视化管理
 * 📦 功能范围：词库列表、折叠展开、颜色分类、词语管理
 * 🔄 架构设计：基于状态驱动的响应式组件，支持实时更新
 */

'use client';

import Button from '@/components/ui/Button';
import CombinedWordLibraryInput from '@/components/ui/CombinedWordLibraryInput';
import type {
  BasicColorType,
  DataLevel,
  WordLibraryKey
} from '@/core/matrix/MatrixTypes';
import {
  AVAILABLE_WORD_LIBRARIES,
  getWordLibraryDisplayName
} from '@/core/wordLibrary/WordLibraryCore';
import {
  useIsLibraryActive,
  useOptimizedLibrary,
  useWordLibraryActions
} from '@/hooks/useOptimizedWordLibraryStore';
import { useStableWordLibraryScroll } from '@/hooks/useStableWordLibraryScroll';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';

// ===== 组件属性 =====

interface WordLibraryManagerProps {
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否为颜色词语模式（可选，用于外部控制） */
  isColorWordMode?: boolean;
}

// ===== 词库项组件 =====

// ===== 词库项组件 =====

interface WordLibraryItemProps {
  color: BasicColorType;
  level: DataLevel;
  libraryKey: WordLibraryKey;
}

const WordLibraryItem: React.FC<WordLibraryItemProps> = memo(({ color, level, libraryKey }) => {
  const itemRef = useRef<HTMLDivElement>(null);

  // 使用优化的状态订阅 - 只订阅当前词库相关状态
  const { library, collapsed } = useOptimizedLibrary(libraryKey);
  const isActiveLibrary = useIsLibraryActive(libraryKey);

  const displayName = useMemo(() => getWordLibraryDisplayName(color, level), [color, level]);

  // 使用ref来跟踪激活状态，避免因为其他状态变化导致的重新渲染
  const isActiveRef = useRef(isActiveLibrary);
  isActiveRef.current = isActiveLibrary;

  // 使用稳定的滚动管理 - 通过父组件传递的滚动函数
  useEffect(() => {
    // 只有在激活状态且匹配当前词库时才滑动
    if (isActiveLibrary && isActiveRef.current !== isActiveLibrary) {
      console.log(`[WordLibraryItem] 检测到激活状态变化: ${libraryKey}`);

      // 延迟执行滑动，确保DOM已更新
      const timer = setTimeout(() => {
        // 通知父组件执行滑动
        const event = new CustomEvent('wordLibraryScrollRequest', {
          detail: { libraryKey, debug: true }
        });
        window.dispatchEvent(event);
      }, 50);

      return () => clearTimeout(timer);
    }
  }, [isActiveLibrary, libraryKey]);

  if (!library) return null;

  return (
    <div ref={itemRef} className="mb-3" data-word-library={libraryKey}>
      {/* 合并式词库输入组件 */}
      <CombinedWordLibraryInput
        libraryKey={libraryKey}
        color={color}
        level={level}
        collapsed={collapsed}
        placeholder={`输入${displayName}词语...`}
        className={isActiveLibrary ? 'word-library-active' : ''}
      />
    </div>
  );
});

WordLibraryItem.displayName = 'WordLibraryItem';



// ===== 主组件 =====

const WordLibraryManagerComponent: React.FC<WordLibraryManagerProps> = memo(({
  className = '',
  style,
  isColorWordMode = true // 默认为true，保持向后兼容
}) => {
  // 使用优化的操作方法，避免状态变化导致重新渲染
  const { resetAllLibraries, exportData, importData } = useWordLibraryActions();

  // 使用稳定的滚动管理
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const {
    scrollToLibrary,
    savePosition,
    restorePosition,
    resetScrollState,
    setScrollContainer
  } = useStableWordLibraryScroll(false); // 关闭调试日志

  // 设置滚动容器
  useEffect(() => {
    if (scrollContainerRef.current) {
      setScrollContainer(scrollContainerRef.current);
    }
  }, [setScrollContainer]);

  // 监听滚动请求事件
  useEffect(() => {
    const handleScrollRequest = (event: CustomEvent) => {
      const { libraryKey, debug } = event.detail;
      scrollToLibrary(libraryKey, { debug });
    };

    window.addEventListener('wordLibraryScrollRequest', handleScrollRequest as EventListener);

    return () => {
      window.removeEventListener('wordLibraryScrollRequest', handleScrollRequest as EventListener);
    };
  }, [scrollToLibrary]);

  // 组件挂载后恢复位置
  useEffect(() => {
    const timer = setTimeout(restorePosition, 50);
    return () => clearTimeout(timer);
  }, [restorePosition]);

  // 组件卸载时重置滚动状态
  useEffect(() => {
    return () => {
      resetScrollState();
    };
  }, [resetScrollState]);

  // 如果不是【颜色】【词语】模式，显示提示
  if (!isColorWordMode) {
    return (
      <div className={`word-library-manager ${className} flex flex-col h-full items-center justify-center`} style={style}>
        <div className="text-center text-gray-500">
          <p className="text-sm">词库管理功能仅在【颜色】【词语】模式下可用</p>
          <p className="text-xs mt-1">请切换到颜色模式 + 词语内容模式</p>
        </div>
      </div>
    );
  }

  // 处理导出
  const handleExport = useCallback(() => {
    const data = exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `词库数据_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [exportData]);

  // 处理导入
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          const success = importData(data);
          if (success) {
            alert('导入成功！');
          } else {
            alert('导入失败，请检查文件格式。');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [importData]);

  // 处理重置
  const handleReset = useCallback(() => {
    if (confirm('确定要清空所有词库吗？此操作不可撤销。')) {
      resetAllLibraries();
    }
  }, [resetAllLibraries]);

  return (
    <div className={`word-library-manager ${className} flex flex-col h-full`} style={style}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-800">词库管理</h3>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleExport}
            title="导出词库数据"
          >
            导出
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={handleImport}
            title="导入词库数据"
          >
            导入
          </Button>
          <Button
            variant="danger"
            size="sm"
            onClick={handleReset}
            title="清空所有词库"
          >
            清空
          </Button>
        </div>
      </div>

      {/* 词库列表 - 可滚动容器 */}
      <div ref={scrollContainerRef} className="flex-1 overflow-y-auto space-y-1 pr-2">
        {AVAILABLE_WORD_LIBRARIES.map(({ color, level }) => {
          const libraryKey = `${color}-${level}` as WordLibraryKey;
          return (
            <WordLibraryItem
              key={libraryKey}
              color={color}
              level={level}
              libraryKey={libraryKey}
            />
          );
        })}
      </div>
    </div>
  );
});

// ===== 性能优化 =====

const WordLibraryManager = memo(WordLibraryManagerComponent);

WordLibraryManager.displayName = 'WordLibraryManager';

export default WordLibraryManager;
export type { WordLibraryManagerProps };
